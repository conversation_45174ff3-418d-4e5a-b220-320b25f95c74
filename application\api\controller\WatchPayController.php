<?php
namespace app\api\controller;

use think\Controller;

class WatchPayController extends Controller
{
    //初始化方法
    protected function initialize()
    {
        parent::initialize();
        header('Access-Control-Allow-Origin:*');
    }

    /**
     * 获取支持的国家列表
     */
    public function getCountries()
    {
        $lang = (input('post.lang')) ? input('post.lang') : 'id';
        $texts = $this->getTexts($lang);

        try {
            // 从充值渠道配置中获取国家列表
            $watchPayConfig = $this->getWatchPayConfig();

            if (!$watchPayConfig) {
                return json([
                    'code' => 0,
                    'msg' => $texts['messages']['get_countries_failed']
                ]);
            }

            $countries = [];
            if (isset($watchPayConfig['countries'])) {
                foreach ($watchPayConfig['countries'] as $countryCode => $countryConfig) {
                    $payTypes = [];
                    if (isset($countryConfig['pay_types'])) {
                        foreach ($countryConfig['pay_types'] as $typeCode => $typeConfig) {
                            if ($typeConfig['enabled']) {
                                $payTypes[] = [
                                    'code' => $typeCode,
                                    'name' => $typeConfig['name'],
                                    'type' => $this->getPayTypeCategory($typeCode)
                                ];
                            }
                        }
                    }

                    $countries[] = [
                        'code' => $countryCode,
                        'name' => $texts['countries'][$countryCode] ?? $countryConfig['name'],
                        'name_en' => $countryConfig['name'],
                        'flag' => $this->getCountryFlag($countryCode),
                        'currency' => $countryConfig['currency'],
                        'min_amount' => $countryConfig['min_amount'],
                        'max_amount' => $countryConfig['max_amount'],
                        'fee_rate' => 2.0, // 可以从配置中读取
                        'pay_types' => $payTypes
                    ];
                }
            }

            // 如果没有配置，返回默认配置
            if (empty($countries)) {
                $countries = [
                    [
                        'code' => 'ID',
                        'name' => $texts['countries']['ID'],
                        'name_en' => 'Indonesia',
                        'flag' => '🇮🇩',
                        'currency' => 'IDR',
                        'min_amount' => 50000,
                        'max_amount' => ********,
                        'fee_rate' => 2.0,
                        'pay_types' => [
                            ['code' => '220', 'name' => $texts['pay_types']['online_banking_b2c_class1'], 'type' => 'online'],
                            ['code' => '223', 'name' => $texts['pay_types']['qris_scan_class2'], 'type' => 'scan']
                        ]
                    ]
                ];
            }

            return json([
                'code' => 1,
                'msg' => $texts['messages']['success'],
                'data' => $countries
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => $texts['messages']['get_countries_failed'] . ': ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 根据渠道ID获取支付方式
     */
    public function getPayTypes()
    {
        $param = input('post.');
        $rechargeId = $param['recharge_id'] ?? '';
        $lang = $param['lang'] ?? 'id';
        $texts = $this->getTexts($lang);

        // 记录调试信息
        \think\facade\Log::info('getPayTypes called with param: ' . json_encode($param));

        try {

            if (empty($rechargeId)) {
                return json(['code' => 0, 'msg' => '渠道ID不能为空']);
            }

            // 根据渠道ID获取配置
            $rechargeType = model('RechangeType')->where(['id' => $rechargeId, 'mode' => 'watchPay', 'state' => 1])->find();

            if (!$rechargeType) {
                return json(['code' => 0, 'msg' => '渠道不存在']);
            }

            // 获取渠道的原始手续费
            $channelFee = $rechargeType['fee'] ?? 0;

            // 从配置文件获取配置
            try {
                $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
                if (!file_exists($configPath)) {
                    $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
                }

                if (!file_exists($configPath)) {
                    return json(['code' => 0, 'msg' => '支付配置文件不存在']);
                }

                $config = include $configPath;
                if (!isset($config['global_pay']['countries'])) {
                    return json(['code' => 0, 'msg' => 'GlobalPay配置格式错误']);
                }
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => '读取配置文件失败: ' . $e->getMessage()]);
            }

            // 获取第一个国家的配置（目前主要支持印尼）
            $countryConfig = reset($config['global_pay']['countries']);
            $countryCode = key($config['global_pay']['countries']);

            $payTypes = [];
            if (isset($countryConfig['pay_types'])) {
                foreach ($countryConfig['pay_types'] as $typeCode => $typeConfig) {
                    if ($typeConfig['enabled']) {
                        // 获取多语言名称
                        $localizedName = $this->getLocalizedPayTypeName($typeCode, $texts);

                        $payTypes[] = [
                            'code' => $typeCode,
                            'name' => $localizedName ?: $typeConfig['name'], // 如果没有多语言版本，使用原名称
                            'type' => $this->getPayTypeCategory($typeCode),
                            'requires_bank_code' => $typeConfig['requires_bank_code'] ?? false // 添加是否需要bank_code字段
                        ];
                    }
                }
            }

            // 如果没有可用的支付类型
            if (empty($payTypes)) {
                return json(['code' => 0, 'msg' => '该渠道暂无可用的支付方式']);
            }

            return json([
                'code' => 1,
                'msg' => $texts['messages']['success'],
                'data' => [
                    'recharge_id' => $rechargeId,
                    'country_code' => $countryCode,
                    'country_name' => $texts['countries'][$countryCode] ?? $countryConfig['name'],
                    'currency' => $countryConfig['currency'],
                    'min_amount' => $countryConfig['min_amount'],
                    'max_amount' => $countryConfig['max_amount'],
                    'fee_rate' => $channelFee,
                    'pay_types' => $payTypes
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => $texts['messages']['get_pay_types_failed'] . ': ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 创建全球支付订单
     */
    public function createOrder()
    {
        $param = input('post.');
        $lang = (input('post.lang')) ? input('post.lang') : 'id';
        $texts = $this->getTexts($lang);

        // 记录调试信息
        \think\facade\Log::info('WatchPay createOrder called with param: ' . json_encode($param));
        \think\facade\Log::info('WatchPay createOrder lang: ' . $lang);

        try {

            // 检查 $param 是否为数组
            if (!is_array($param)) {
                \think\facade\Log::error('WatchPay createOrder param is not array: ' . var_export($param, true));
                return json(['code' => 0, 'msg' => '参数格式错误']);
            }

            // 参数验证
            $required = ['token', 'recharge_id', 'pay_type', 'amount'];
            foreach ($required as $field) {
                if (!isset($param[$field]) || $param[$field] === '' || $param[$field] === null) {
                    return json(['code' => 0, 'msg' => $texts['messages']['param_required'] . ": {$field}"]);
                }
            }

            // 验证用户token
            $token = $param['token'];
            $userArr = explode(',', auth_code($token, 'DECODE'));
            $uid = $userArr[0] ?? 0;
            $username = $userArr[1] ?? '';

            if (empty($uid)) {
                return json(['code' => 0, 'msg' => $texts['messages']['invalid_login']]);
            }

            // 验证渠道ID并获取渠道信息
            $rechargeType = model('RechangeType')->where('id', $param['recharge_id'])->where('state', 1)->find();
            if (!$rechargeType) {
                return json(['code' => 0, 'msg' => '充值渠道不存在或已禁用']);
            }

            if ($rechargeType['mode'] !== 'watchPay') {
                return json(['code' => 0, 'msg' => '渠道类型错误，非WatchPay支付渠道']);
            }

            // 生成订单号
            $orderNo = 'GP' . date('YmdHis') . rand(1000, 9999);

            // 创建充值记录
            $rechargeData = [
                'uid' => $uid,
                'order_number' => $orderNo,
                'type' => $param['recharge_id'], // 使用传入的渠道ID
                'money' => $param['amount'],
                'daozhang_money' => $param['amount'],
                'state' => 3, // 待支付
                'add_time' => time(),
                'postscript' => $rechargeType['name'],
                'remarks' => json_encode([
                    'pay_type' => $param['pay_type'],
                    'pay_method' => 'watchPay'
                ])
            ];

            $rechargeId = model('UserRecharge')->insertGetId($rechargeData);
            
            if (!$rechargeId) {
                return json(['code' => 0, 'msg' => '订单创建失败']);
            }

            // 调用第三方支付API
            $thirdPayService = new \app\common\service\ThirdPayService();
            $orderData = [
                'recharge_id' => $param['recharge_id'],
                'pay_type' => $param['pay_type'],
                'order_no' => $orderNo,
                'amount' => $param['amount'],
                'notify_url' => $this->getNotifyUrl($param['recharge_id'])
            ];

            // 如果前端传递了bank_code，添加到订单数据中
            if (!empty($param['bank_code'])) {
                $orderData['bank_code'] = $param['bank_code'];
                \think\facade\Log::info('WatchPay received bank_code: ' . $param['bank_code']);
            }

            $result = $thirdPayService->createOrder($orderData);

            // 记录调试信息
            \think\facade\Log::info('ThirdPayService result: ' . json_encode($result));

            // 检查返回结果类型
            if (!is_array($result)) {
                // 删除失败的订单
                model('UserRecharge')->where('id', $rechargeId)->delete();
                \think\facade\Log::error('ThirdPayService returned non-array result: ' . var_export($result, true));
                return json([
                    'code' => 0,
                    'msg' => '第三方支付服务返回格式错误'
                ]);
            }

            if (isset($result['code']) && $result['code'] == 1) {
                // 检查是否有支付URL
                if (!isset($result['data']) || !is_array($result['data']) || !isset($result['data']['pay_url'])) {
                    // 删除失败的订单
                    model('UserRecharge')->where('id', $rechargeId)->delete();
                    return json([
                        'code' => 0,
                        'msg' => '第三方支付服务未返回支付链接'
                    ]);
                }

                // 更新充值记录
                model('UserRecharge')->where('id', $rechargeId)->update([
                    'submitUrl' => $result['data']['pay_url']
                ]);

                return json([
                    'code' => 1,
                    'msg' => '订单创建成功',
                    'data' => [
                        'order_no' => $orderNo,
                        'pay_url' => $result['data']['pay_url'],
                        'amount' => $param['amount'],
                        'recharge_id' => $rechargeId
                    ]
                ]);
            } else {
                // 删除失败的订单
                model('UserRecharge')->where('id', $rechargeId)->delete();

                // 返回错误信息
                $errorMsg = isset($result['msg']) ? $result['msg'] : '支付订单创建失败';
                return json([
                    'code' => 0,
                    'msg' => $errorMsg
                ]);
            }

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '创建订单失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取回调URL
     */
    private function getNotifyUrl($rechargeId)
    {
        // 优先从配置文件获取统一回调URL
        $notifyUrl = $this->getNotifyUrlFromConfig();

        if ($notifyUrl) {
            return $notifyUrl;
        }

        // 兜底方案：从全局配置获取默认域名生成统一充值回调地址
        try {
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (file_exists($configPath)) {
                $config = include $configPath;
                $defaultDomain = $config['global']['default_notify_domain'] ?? 'http://localhost';
                return rtrim($defaultDomain, '/') . '/api/transaction/unifiedCallback';
            }
        } catch (\Exception $e) {
            // 配置文件读取失败，使用最终兜底方案
        }

        // 最终兜底方案：自动检测当前服务器信息
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] ?? 'localhost';

        return $protocol . '://' . $host . '/api/transaction/unifiedCallback';
    }

    /**
     * 从配置文件获取回调URL
     */
    private function getNotifyUrlFromConfig()
    {
        try {
            // 直接读取配置文件
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (!file_exists($configPath)) {
                return null;
            }

            $paymentConfig = include($configPath);

            // 直接使用全局统一充值回调地址
            return $paymentConfig['global']['unified_recharge_callback_url'] ?? null;

        } catch (\Exception $e) {
            \think\facade\Log::error("Failed to get notify URL from config: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 从配置文件获取回调域名
     */
    private function getNotifyDomainFromConfig($rechargeId)
    {
        try {
            // 直接读取配置文件，修复路径问题
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (!file_exists($configPath)) {
                return null;
            }

            $paymentConfig = include($configPath);

            if (!isset($paymentConfig['watch_pay']['enabled']) || !$paymentConfig['watch_pay']['enabled']) {
                return null;
            }

            $watchPayConfig = $paymentConfig['watch_pay'];

            // 目前只支持印尼，直接使用ID配置
            $countryConfig = $watchPayConfig['countries']['ID'] ?? null;
            if ($countryConfig && $countryConfig['enabled'] && !empty($countryConfig['notify_domain'])) {
                return $countryConfig['notify_domain'];
            }

            // 使用全局默认回调域名
            return $paymentConfig['global']['default_notify_domain'] ?? null;

        } catch (\Exception $e) {
            \think\facade\Log::error("Failed to get notify domain from config: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 根据渠道名称推断国家代码
     */
    private function getCountryCodeFromName($name)
    {
        if (stripos($name, '印尼') !== false || stripos($name, 'Indonesia') !== false) {
            return 'ID';
        } elseif (stripos($name, '印度') !== false || stripos($name, 'India') !== false) {
            return 'IN';
        } elseif (stripos($name, '泰国') !== false || stripos($name, 'Thailand') !== false) {
            return 'TH';
        } elseif (stripos($name, '巴西') !== false || stripos($name, 'Brazil') !== false) {
            return 'BR';
        } elseif (stripos($name, '越南') !== false || stripos($name, 'Vietnam') !== false) {
            return 'VN';
        } elseif (stripos($name, '马来') !== false || stripos($name, 'Malaysia') !== false) {
            return 'MY';
        }

        // 默认返回印尼
        return 'ID';
    }

    /**
     * 支付回调处理
     */
    public function notify()
    {
        try {
            $params = input('post.');
            
            // 记录回调日志
            \think\facade\Log::info('WatchPay notify: ' . json_encode($params));

            // 调用第三方支付服务处理回调
            $thirdPayService = new \app\common\service\ThirdPayService();
            $result = $thirdPayService->handleNotify($params);

            if ($result) {
                echo 'success';
            } else {
                echo 'fail';
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('WatchPay notify error: ' . $e->getMessage());
            echo 'fail';
        }
    }



    /**
     * 获取支付方式的多语言名称
     */
    private function getLocalizedPayTypeName($typeCode, $texts)
    {
        // 支付方式代码到多语言键的映射
        $payTypeMapping = [
            '220' => 'online_banking_b2c_class1',
            '223' => 'qris_scan_class2',
            '101' => 'paytm_native1',
            '104' => 'paytm_entertainment',
            '105' => 'upi_scan',
            '122' => 'upi_category2',
            '131' => 'paytm_category1',
            '132' => 'upi_category1',
            '152' => 'upi_native2'
        ];

        $langKey = $payTypeMapping[$typeCode] ?? null;
        return $langKey ? ($texts['pay_types'][$langKey] ?? null) : null;
    }

    /**
     * 获取多语言文本（高效版本，遵循现有系统模式）
     */
    private function getTexts($lang)
    {
        $texts = [];

        // 国家名称
        if($lang=='cn') {
            $texts['countries'] = [
                'ID' => '印尼', 'IN' => '印度', 'TH' => '泰国', 'VN' => '越南', 'MY' => '马来西亚'
            ];
            $texts['pay_types'] = [
                'online_banking_b2c_class1' => '网银B2C一类', 'qris_scan_class2' => 'QRIS扫码二类', 'online_banking' => '网银支付',
                'paytm' => 'Paytm钱包', 'upi' => 'UPI支付', 'online_banking_b2c' => '网银B2C',
                'supex' => 'SUPEX扫码', 'truemoney' => 'TrueMoney钱包', 'promptpay' => 'PromptPay扫码',
                'momo' => 'MOMO钱包', 'zalopay' => 'Zalo Pay钱包', 'online_banking_direct' => '网银直连',
                'scan_payment' => '扫码支付', 'ewallet' => '电子钱包', 'online_banking_card' => '网银转卡',
                // 新增支付方式
                'paytm_entertainment' => 'Paytm娱乐', 'paytm_scan' => 'Paytm扫码', 'upi_scan' => 'UPI扫码',
                'upi_wallet' => 'UPI钱包', 'paytm_category1' => 'Paytm跑分一类', 'paytm_native1' => 'Paytm原生一类',
                'upi_category2' => 'UPI跑分二类', 'upi_native2' => 'UPI原生二类', 'upi_category1' => 'UPI跑分一类'
            ];
            $texts['messages'] = [
                'success' => '成功', 'country_code_required' => '国家代码不能为空',
                'invalid_country' => '不支持的国家', 'param_required' => '参数不能为空',
                'invalid_login' => '用户登录信息无效', 'get_countries_failed' => '获取国家列表失败',
                'get_pay_types_failed' => '获取支付方式失败', 'order_created' => '订单创建成功'
            ];
        } elseif($lang=='en') {
            $texts['countries'] = [
                'ID' => 'Indonesia', 'IN' => 'India', 'TH' => 'Thailand', 'VN' => 'Vietnam', 'MY' => 'Malaysia'
            ];
            $texts['pay_types'] = [
                'ovo_wallet' => 'OVO Wallet', 'qris_scan' => 'QRIS Scan', 'online_banking' => 'Online Banking',
                'paytm' => 'Paytm Wallet', 'upi' => 'UPI Payment', 'online_banking_b2c' => 'Online Banking B2C',
                'supex' => 'SUPEX Scan', 'truemoney' => 'TrueMoney Wallet', 'promptpay' => 'PromptPay Scan',
                'momo' => 'MOMO Wallet', 'zalopay' => 'Zalo Pay Wallet', 'online_banking_direct' => 'Direct Online Banking',
                'scan_payment' => 'Scan Payment', 'ewallet' => 'E-Wallet', 'online_banking_card' => 'Online Banking Card Transfer',
                // 新增支付方式
                'paytm_entertainment' => 'Paytm Entertainment', 'paytm_scan' => 'Paytm Scan', 'upi_scan' => 'UPI Scan',
                'upi_wallet' => 'UPI Wallet', 'paytm_category1' => 'Paytm Category 1', 'paytm_native1' => 'Paytm Native 1',
                'upi_category2' => 'UPI Category 2', 'upi_native2' => 'UPI Native 2', 'upi_category1' => 'UPI Category 1'
            ];
            $texts['messages'] = [
                'success' => 'Success', 'country_code_required' => 'Country code is required',
                'invalid_country' => 'Unsupported country', 'param_required' => 'Parameter is required',
                'invalid_login' => 'Invalid user login information', 'get_countries_failed' => 'Failed to get countries list',
                'get_pay_types_failed' => 'Failed to get payment types', 'order_created' => 'Order created successfully'
            ];
        } elseif($lang=='id') {
            $texts['countries'] = [
                'ID' => 'Indonesia', 'IN' => 'India', 'TH' => 'Thailand', 'VN' => 'Vietnam', 'MY' => 'Malaysia'
            ];
            $texts['pay_types'] = [
                'online_banking_b2c_class1' => 'Internet Banking B2C Kelas 1', 'qris_scan_class2' => 'Scan QRIS Kelas 2', 'online_banking' => 'Internet Banking',
                'paytm' => 'Dompet Paytm', 'upi' => 'Pembayaran UPI', 'online_banking_b2c' => 'Internet Banking B2C',
                'supex' => 'Scan SUPEX', 'truemoney' => 'Dompet TrueMoney', 'promptpay' => 'Scan PromptPay',
                'momo' => 'Dompet MOMO', 'zalopay' => 'Dompet Zalo Pay', 'online_banking_direct' => 'Internet Banking Langsung',
                'scan_payment' => 'Pembayaran Scan', 'ewallet' => 'Dompet Elektronik', 'online_banking_card' => 'Transfer Kartu Internet Banking',
                // 新增支付方式
                'paytm_entertainment' => 'Paytm Hiburan', 'paytm_scan' => 'Scan Paytm', 'upi_scan' => 'Scan UPI',
                'upi_wallet' => 'Dompet UPI', 'paytm_category1' => 'Paytm Kategori 1', 'paytm_native1' => 'Paytm Asli 1',
                'upi_category2' => 'UPI Kategori 2', 'upi_native2' => 'UPI Asli 2', 'upi_category1' => 'UPI Kategori 1'
            ];
            $texts['messages'] = [
                'success' => 'Berhasil', 'country_code_required' => 'Kode negara diperlukan',
                'invalid_country' => 'Negara tidak didukung', 'param_required' => 'Parameter diperlukan',
                'invalid_login' => 'Informasi login pengguna tidak valid', 'get_countries_failed' => 'Gagal mendapatkan daftar negara',
                'get_pay_types_failed' => 'Gagal mendapatkan jenis pembayaran', 'order_created' => 'Pesanan berhasil dibuat'
            ];
        } elseif($lang=='ft') {
            $texts['countries'] = [
                'ID' => '印尼', 'IN' => '印度', 'TH' => '泰國', 'VN' => '越南', 'MY' => '馬來西亞'
            ];
            $texts['pay_types'] = [
                'online_banking_b2c_class1' => '網銀B2C一類', 'qris_scan_class2' => 'QRIS掃碼二類', 'online_banking' => '網銀支付',
                'paytm' => 'Paytm錢包', 'upi' => 'UPI支付', 'online_banking_b2c' => '網銀B2C',
                'supex' => 'SUPEX掃碼', 'truemoney' => 'TrueMoney錢包', 'promptpay' => 'PromptPay掃碼',
                'momo' => 'MOMO錢包', 'zalopay' => 'Zalo Pay錢包', 'online_banking_direct' => '網銀直連',
                'scan_payment' => '掃碼支付', 'ewallet' => '電子錢包', 'online_banking_card' => '網銀轉卡',
                // 新增支付方式
                'paytm_entertainment' => 'Paytm娛樂', 'paytm_scan' => 'Paytm掃碼', 'upi_scan' => 'UPI掃碼',
                'upi_wallet' => 'UPI錢包', 'paytm_category1' => 'Paytm跑分一類', 'paytm_native1' => 'Paytm原生一類',
                'upi_category2' => 'UPI跑分二類', 'upi_native2' => 'UPI原生二類', 'upi_category1' => 'UPI跑分一類'
            ];
            $texts['messages'] = [
                'success' => '成功', 'country_code_required' => '國家代碼不能為空',
                'invalid_country' => '不支持的國家', 'param_required' => '參數不能為空',
                'invalid_login' => '用戶登錄信息無效', 'get_countries_failed' => '獲取國家列表失敗',
                'get_pay_types_failed' => '獲取支付方式失敗', 'order_created' => '訂單創建成功'
            ];
        } elseif($lang=='yd') {
            $texts['countries'] = [
                'ID' => 'इंडोनेशिया', 'IN' => 'भारत', 'TH' => 'थाईलैंड', 'VN' => 'वियतनाम', 'MY' => 'मलेशिया'
            ];
            $texts['pay_types'] = [
                'ovo_wallet' => 'OVO वॉलेट', 'qris_scan' => 'QRIS स्कैन', 'online_banking' => 'ऑनलाइन बैंकिंग',
                'paytm' => 'Paytm वॉलेट', 'upi' => 'UPI भुगतान', 'online_banking_b2c' => 'ऑनलाइन बैंकिंग B2C',
                'supex' => 'SUPEX स्कैन', 'truemoney' => 'TrueMoney वॉलेट', 'promptpay' => 'PromptPay स्कैन',
                'momo' => 'MOMO वॉलेट', 'zalopay' => 'Zalo Pay वॉलेट', 'online_banking_direct' => 'प्रत्यक्ष ऑनलाइन बैंकिंग',
                'scan_payment' => 'स्कैन भुगतान', 'ewallet' => 'ई-वॉलेट', 'online_banking_card' => 'ऑनलाइन बैंकिंग कार्ड ट्रांसफर'
            ];
            $texts['messages'] = [
                'success' => 'सफलता', 'country_code_required' => 'देश कोड आवश्यक है',
                'invalid_country' => 'असमर्थित देश', 'param_required' => 'पैरामीटर आवश्यक है',
                'invalid_login' => 'अमान्य उपयोगकर्ता लॉगिन जानकारी', 'get_countries_failed' => 'देशों की सूची प्राप्त करने में विफल',
                'get_pay_types_failed' => 'भुगतान प्रकार प्राप्त करने में विफल', 'order_created' => 'ऑर्डर सफलतापूर्वक बनाया गया'
            ];
        } elseif($lang=='vi') {
            $texts['countries'] = [
                'ID' => 'Indonesia', 'IN' => 'Ấn Độ', 'TH' => 'Thái Lan', 'VN' => 'Việt Nam', 'MY' => 'Malaysia'
            ];
            $texts['pay_types'] = [
                'ovo_wallet' => 'Ví OVO', 'qris_scan' => 'Quét QRIS', 'online_banking' => 'Ngân hàng trực tuyến',
                'paytm' => 'Ví Paytm', 'upi' => 'Thanh toán UPI', 'online_banking_b2c' => 'Ngân hàng trực tuyến B2C',
                'supex' => 'Quét SUPEX', 'truemoney' => 'Ví TrueMoney', 'promptpay' => 'Quét PromptPay',
                'momo' => 'Ví MOMO', 'zalopay' => 'Ví Zalo Pay', 'online_banking_direct' => 'Ngân hàng trực tuyến trực tiếp',
                'scan_payment' => 'Thanh toán quét', 'ewallet' => 'Ví điện tử', 'online_banking_card' => 'Chuyển thẻ ngân hàng trực tuyến'
            ];
            $texts['messages'] = [
                'success' => 'Thành công', 'country_code_required' => 'Mã quốc gia là bắt buộc',
                'invalid_country' => 'Quốc gia không được hỗ trợ', 'param_required' => 'Tham số là bắt buộc',
                'invalid_login' => 'Thông tin đăng nhập người dùng không hợp lệ', 'get_countries_failed' => 'Không thể lấy danh sách quốc gia',
                'get_pay_types_failed' => 'Không thể lấy loại thanh toán', 'order_created' => 'Đơn hàng được tạo thành công'
            ];
        } elseif($lang=='es') {
            $texts['countries'] = [
                'ID' => 'Indonesia', 'IN' => 'India', 'TH' => 'Tailandia', 'VN' => 'Vietnam', 'MY' => 'Malasia'
            ];
            $texts['pay_types'] = [
                'ovo_wallet' => 'Billetera OVO', 'qris_scan' => 'Escaneo QRIS', 'online_banking' => 'Banca en línea',
                'paytm' => 'Billetera Paytm', 'upi' => 'Pago UPI', 'online_banking_b2c' => 'Banca en línea B2C',
                'supex' => 'Escaneo SUPEX', 'truemoney' => 'Billetera TrueMoney', 'promptpay' => 'Escaneo PromptPay',
                'momo' => 'Billetera MOMO', 'zalopay' => 'Billetera Zalo Pay', 'online_banking_direct' => 'Banca en línea directa',
                'scan_payment' => 'Pago por escaneo', 'ewallet' => 'Billetera electrónica', 'online_banking_card' => 'Transferencia de tarjeta bancaria en línea'
            ];
            $texts['messages'] = [
                'success' => 'Éxito', 'country_code_required' => 'Se requiere código de país',
                'invalid_country' => 'País no compatible', 'param_required' => 'Se requiere parámetro',
                'invalid_login' => 'Información de inicio de sesión de usuario no válida', 'get_countries_failed' => 'Error al obtener la lista de países',
                'get_pay_types_failed' => 'Error al obtener tipos de pago', 'order_created' => 'Pedido creado exitosamente'
            ];
        } elseif($lang=='ja') {
            $texts['countries'] = [
                'ID' => 'インドネシア', 'IN' => 'インド', 'TH' => 'タイ', 'VN' => 'ベトナム', 'MY' => 'マレーシア'
            ];
            $texts['pay_types'] = [
                'ovo_wallet' => 'OVOウォレット', 'qris_scan' => 'QRISスキャン', 'online_banking' => 'オンラインバンキング',
                'paytm' => 'Paytmウォレット', 'upi' => 'UPI決済', 'online_banking_b2c' => 'オンラインバンキングB2C',
                'supex' => 'SUPEXスキャン', 'truemoney' => 'TrueMoneyウォレット', 'promptpay' => 'PromptPayスキャン',
                'momo' => 'MOMOウォレット', 'zalopay' => 'Zalo Payウォレット', 'online_banking_direct' => 'ダイレクトオンラインバンキング',
                'scan_payment' => 'スキャン決済', 'ewallet' => '電子ウォレット', 'online_banking_card' => 'オンラインバンキングカード転送'
            ];
            $texts['messages'] = [
                'success' => '成功', 'country_code_required' => '国コードが必要です',
                'invalid_country' => 'サポートされていない国', 'param_required' => 'パラメータが必要です',
                'invalid_login' => '無効なユーザーログイン情報', 'get_countries_failed' => '国リストの取得に失敗しました',
                'get_pay_types_failed' => '支払いタイプの取得に失敗しました', 'order_created' => '注文が正常に作成されました'
            ];
        } elseif($lang=='th') {
            $texts['countries'] = [
                'ID' => 'อินโดนีเซีย', 'IN' => 'อินเดีย', 'TH' => 'ประเทศไทย', 'VN' => 'เวียดนาม', 'MY' => 'มาเลเซีย'
            ];
            $texts['pay_types'] = [
                'ovo_wallet' => 'กระเป๋า OVO', 'qris_scan' => 'สแกน QRIS', 'online_banking' => 'ธนาคารออนไลน์',
                'paytm' => 'กระเป๋า Paytm', 'upi' => 'การชำระเงิน UPI', 'online_banking_b2c' => 'ธนาคารออนไลน์ B2C',
                'supex' => 'สแกน SUPEX', 'truemoney' => 'กระเป๋าเงิน TrueMoney', 'promptpay' => 'สแกน PromptPay',
                'momo' => 'กระเป๋า MOMO', 'zalopay' => 'กระเป๋า Zalo Pay', 'online_banking_direct' => 'ธนาคารออนไลน์โดยตรง',
                'scan_payment' => 'การชำระเงินแบบสแกน', 'ewallet' => 'กระเป๋าเงินอิเล็กทรอนิกส์', 'online_banking_card' => 'การโอนบัตรธนาคารออนไลน์'
            ];
            $texts['messages'] = [
                'success' => 'สำเร็จ', 'country_code_required' => 'จำเป็นต้องมีรหัสประเทศ',
                'invalid_country' => 'ประเทศที่ไม่รองรับ', 'param_required' => 'จำเป็นต้องมีพารามิเตอร์',
                'invalid_login' => 'ข้อมูลการเข้าสู่ระบบของผู้ใช้ไม่ถูกต้อง', 'get_countries_failed' => 'ไม่สามารถรับรายการประเทศได้',
                'get_pay_types_failed' => 'ไม่สามารถรับประเภทการชำระเงินได้', 'order_created' => 'สร้างคำสั่งซื้อสำเร็จ'
            ];
        } elseif($lang=='ma') {
            $texts['countries'] = [
                'ID' => 'Indonesia', 'IN' => 'India', 'TH' => 'Thailand', 'VN' => 'Vietnam', 'MY' => 'Malaysia'
            ];
            $texts['pay_types'] = [
                'online_banking_b2c_class1' => 'Perbankan dalam talian B2C Kelas 1', 'qris_scan_class2' => 'Imbas QRIS Kelas 2', 'online_banking' => 'Perbankan dalam talian',
                'paytm' => 'Dompet Paytm', 'upi' => 'Pembayaran UPI', 'online_banking_b2c' => 'Perbankan dalam talian B2C',
                'supex' => 'Imbas SUPEX', 'truemoney' => 'Dompet TrueMoney', 'promptpay' => 'Imbas PromptPay',
                'momo' => 'Dompet MOMO', 'zalopay' => 'Dompet Zalo Pay', 'online_banking_direct' => 'Perbankan dalam talian langsung',
                'scan_payment' => 'Pembayaran imbas', 'ewallet' => 'Dompet elektronik', 'online_banking_card' => 'Pemindahan kad perbankan dalam talian'
            ];
            $texts['messages'] = [
                'success' => 'Berjaya', 'country_code_required' => 'Kod negara diperlukan',
                'invalid_country' => 'Negara tidak disokong', 'param_required' => 'Parameter diperlukan',
                'invalid_login' => 'Maklumat log masuk pengguna tidak sah', 'get_countries_failed' => 'Gagal mendapatkan senarai negara',
                'get_pay_types_failed' => 'Gagal mendapatkan jenis pembayaran', 'order_created' => 'Pesanan berjaya dicipta'
            ];
        } elseif($lang=='pt') {
            $texts['countries'] = [
                'ID' => 'Indonésia', 'IN' => 'Índia', 'TH' => 'Tailândia', 'VN' => 'Vietnã', 'MY' => 'Malásia'
            ];
            $texts['pay_types'] = [
                'online_banking_b2c_class1' => 'Banco online B2C Classe 1', 'qris_scan_class2' => 'Digitalização QRIS Classe 2', 'online_banking' => 'Banco online',
                'paytm' => 'Carteira Paytm', 'upi' => 'Pagamento UPI', 'online_banking_b2c' => 'Banco online B2C',
                'supex' => 'Digitalização SUPEX', 'truemoney' => 'Carteira TrueMoney', 'promptpay' => 'Digitalização PromptPay',
                'momo' => 'Carteira MOMO', 'zalopay' => 'Carteira Zalo Pay', 'online_banking_direct' => 'Banco online direto',
                'scan_payment' => 'Pagamento por digitalização', 'ewallet' => 'Carteira eletrônica', 'online_banking_card' => 'Transferência de cartão bancário online'
            ];
            $texts['messages'] = [
                'success' => 'Sucesso', 'country_code_required' => 'Código do país é obrigatório',
                'invalid_country' => 'País não suportado', 'param_required' => 'Parâmetro é obrigatório',
                'invalid_login' => 'Informações de login do usuário inválidas', 'get_countries_failed' => 'Falha ao obter lista de países',
                'get_pay_types_failed' => 'Falha ao obter tipos de pagamento', 'order_created' => 'Pedido criado com sucesso'
            ];
        } else {
            // 默认印尼语
            $texts['countries'] = [
                'ID' => 'Indonesia', 'IN' => 'India', 'TH' => 'Thailand', 'VN' => 'Vietnam', 'MY' => 'Malaysia'
            ];
            $texts['pay_types'] = [
                'online_banking_b2c_class1' => 'Internet Banking B2C Kelas 1', 'qris_scan_class2' => 'Scan QRIS Kelas 2', 'online_banking' => 'Internet Banking',
                'paytm' => 'Dompet Paytm', 'upi' => 'Pembayaran UPI', 'online_banking_b2c' => 'Internet Banking B2C',
                'supex' => 'Scan SUPEX', 'truemoney' => 'Dompet TrueMoney', 'promptpay' => 'Scan PromptPay',
                'momo' => 'Dompet MOMO', 'zalopay' => 'Dompet Zalo Pay', 'online_banking_direct' => 'Internet Banking Langsung',
                'scan_payment' => 'Pembayaran Scan', 'ewallet' => 'Dompet Elektronik', 'online_banking_card' => 'Transfer Kartu Internet Banking',
                // 新增支付方式
                'paytm_entertainment' => 'Paytm Hiburan', 'paytm_scan' => 'Scan Paytm', 'upi_scan' => 'Scan UPI',
                'upi_wallet' => 'Dompet UPI', 'paytm_category1' => 'Paytm Kategori 1', 'paytm_native1' => 'Paytm Asli 1',
                'upi_category2' => 'UPI Kategori 2', 'upi_native2' => 'UPI Asli 2', 'upi_category1' => 'UPI Kategori 1'
            ];
            $texts['messages'] = [
                'success' => 'Berhasil', 'country_code_required' => 'Kode negara diperlukan',
                'invalid_country' => 'Negara tidak didukung', 'param_required' => 'Parameter diperlukan',
                'invalid_login' => 'Informasi login pengguna tidak valid', 'get_countries_failed' => 'Gagal mendapatkan daftar negara',
                'get_pay_types_failed' => 'Gagal mendapatkan jenis pembayaran', 'order_created' => 'Pesanan berhasil dibuat'
            ];
        }

        return $texts;
    }

    /**
     * 获取WatchPay配置
     */
    private function getWatchPayConfig()
    {
        try {
            // 直接读取配置文件，修复路径问题
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (file_exists($configPath)) {
                $paymentConfig = include($configPath);
                $watchPayConfig = $paymentConfig['watch_pay'] ?? null;

                if ($watchPayConfig && $watchPayConfig['enabled']) {
                    \think\facade\Log::info('WatchPay config loaded from file: ' . json_encode($watchPayConfig));
                    return $watchPayConfig;
                }
            }

            \think\facade\Log::info('WatchPay config not found or disabled');
            return null;

        } catch (\Exception $e) {
            \think\facade\Log::error('WatchPay config error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取支付类型分类
     */
    private function getPayTypeCategory($typeCode)
    {
        $categories = [
            '220' => 'online',  // 网银B2C一类
            '223' => 'scan',    // QRIS扫码二类
            '104' => 'wallet',  // Paytm娱乐
            '131' => 'wallet',  // Paytm跑分一类
            '101' => 'wallet',  // Paytm原生一类
            '105' => 'online',  // UPI娱乐
            '122' => 'online',  // UPI跑分二类
            '152' => 'online',  // UPI原生二类
            '132' => 'online',  // UPI跑分一类
        ];

        return $categories[$typeCode] ?? 'online';
    }

    /**
     * 获取国家旗帜
     */
    private function getCountryFlag($countryCode)
    {
        $flags = [
            'ID' => '🇮🇩',
            'IN' => '🇮🇳',
            'TH' => '🇹🇭',
            'VN' => '🇻🇳',
            'MY' => '🇲🇾',
            'BR' => '🇧🇷'
        ];

        return $flags[$countryCode] ?? '🌍';
    }
}
