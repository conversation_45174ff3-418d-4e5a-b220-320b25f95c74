webpackJsonp([30],{nbhr:function(t,e){},uE8p:function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s={render:function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"PageBox"},[o("div",{staticClass:"ScrollBox"},[o("div",{staticClass:"Robot"},[o("h2",[t._v(t._s(t.$t("user.robot[1]")))]),t._v(" "),o("p",[t._v(t._s(t.$t("user.robot[2]")))]),t._v(" "),o("p",[t._v(t._s(t.$t("user.robot[3]")))]),t._v(" "),o("van-button",{staticClass:"mt15",staticStyle:{"font-size":"16px"},attrs:{type:"danger",block:"",disabled:1==t.UserInfo.is_housekeeper},on:{click:t.onSubmit}},[t._v(t._s(t.$t("user.robot[4]")))])],1)])])},staticRenderFns:[]};var n=o("VU/8")({name:"Robot",components:{},props:[],data:function(){return{}},computed:{},watch:{},created:function(){this.$parent.navBarTitle=this.$t("user.robot[0]")},mounted:function(){},activated:function(){},destroyed:function(){},methods:{onSubmit:function(){this.$Model.SetUserInfo({is_housekeeper:1})}}},s,!1,function(t){o("nbhr")},"data-v-39b2fb5a",null);e.default=n.exports}});