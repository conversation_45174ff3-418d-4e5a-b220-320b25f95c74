webpackJsonp([24],{fOjX:function(t,s,i){"use strict";Object.defineProperty(s,"__esModule",{value:!0});var a={name:"TaskList",components:{},props:["taskType","taskGrade"],data:function(){return{listData:"",isLoad:!1,isFinished:!1,isRefresh:!1,pageNo:1}},computed:{},watch:{},created:function(){this.getListData("init")},mounted:function(){},activated:function(){},destroyed:function(){},methods:{onLoad:function(){this.getListData("load")},getListData:function(t){var s=this;this.isLoad=!0,this.isRefresh=!1,"load"==t?this.pageNo+=1:(this.pageNo=1,this.isFinished=!1),this.$Model.GetTaskList({group_id:this.taskType,page_no:this.pageNo,is_u:0},function(i){s.$nextTick(function(){s.isLoad=!1}),1==i.code?("load"==t?1==s.pageNo?s.listData=i.info:s.listData=s.listData.concat(i.info):s.listData=i.info,s.pageNo==i.data_total_page?s.isFinished=!0:s.isFinished=!1):(s.listData="",s.isFinished=!0)})},onRefresh:function(){this.getListData("init")},receiveTask:function(t,s){var i=this;localStorage.Token?this.$Model.ReceiveTask(t,function(t){1==t.code&&(s.is_l=1,i.pageNo=0)}):this.$router.push("/login")}}},e={render:function(){var t=this,s=t.$createElement,i=t._self._c||s;return i("div",{staticClass:"Site PageBox"},[i("van-nav-bar",{attrs:{fixed:"",border:!1,title:t.$t("task.list[0]"),"left-arrow":""},on:{"click-left":function(s){return t.$router.go(-1)}}}),t._v(" "),i("div",{staticClass:"ScrollBox"},[i("van-pull-refresh",{on:{refresh:t.onRefresh},model:{value:t.isRefresh,callback:function(s){t.isRefresh=s},expression:"isRefresh"}},[i("van-list",{class:{Empty:!t.listData.length},attrs:{finished:t.isFinished,"finished-text":t.listData.length?t.$t("vanPull[0]"):t.$t("vanPull[1]")},on:{load:t.onLoad},model:{value:t.isLoad,callback:function(s){t.isLoad=s},expression:"isLoad"}},t._l(t.listData,function(s){return i("van-cell",{key:t.InitData.setting.up_url+s.task_id,staticClass:"TaskItem",attrs:{border:!1,to:"/taskShow/"+s.task_id},scopedSlots:t._u([{key:"title",fn:function(){return[i("div",[i("span",[t._v(t._s(t.$t("task.list[1]"))+":"+t._s(s.username))]),t._v(" "),i("i",[t._v(t._s(s.status_dec))])]),t._v(" "),i("div",[i("span",[t._v(t._s(t.$t("task.list[2]"))+":"),i("b",[t._v(t._s(s.surplus_number))])]),t._v(" "),i("span",[t._v("\n                "+t._s(t.InitData.currency)+"\n                "),i("em",[t._v(t._s(Number(s.reward_price)))])])]),t._v(" "),i("div",[i("span",[t._v(t._s(t.$t("task.list[3]"))+":"+t._s(s.group_info))]),t._v(" "),i("span",[i("van-button",{attrs:{type:"info",size:"mini",disabled:0!=s.is_l},on:{click:function(i){return i.stopPropagation(),t.receiveTask(s.task_id,s)}}},[t._v(t._s(t.$t("task.list[4]")))])],1)])]},proxy:!0}],null,!0)},[i("div",{staticClass:"icon",attrs:{slot:"icon"},slot:"icon"},[i("h4",[t._v(t._s(s.group_name))]),t._v(" "),i("a",{attrs:{href:"javascript:;"}},[i("img",{attrs:{src:t.InitData.setting.up_url+s.icon}})]),t._v(" "),i("van-tag",{attrs:{type:"primary"}},[t._v(t._s(s.vip_dec))])],1)])}),1)],1)],1)],1)},staticRenderFns:[]};var n=i("VU/8")(a,e,!1,function(t){i("qOsZ")},"data-v-61aaebcd",null);s.default=n.exports},qOsZ:function(t,s){}});