webpackJsonp([22],{J79f:function(t,s,e){"use strict";Object.defineProperty(s,"__esModule",{value:!0});var a={name:"WatchPayOrder",props:["orderNumber"],data:function(){return{orderInfo:{},isRefreshing:!1,isRetrying:!1,isCancelling:!1,refreshTimer:null}},computed:{canRetry:function(){return["failed","expired"].includes(this.orderInfo.status)},canCancel:function(){return["pending","processing"].includes(this.orderInfo.status)},webTitle:function(){return this.$store.state.InitData&&this.$store.state.InitData.setting&&this.$store.state.InitData.setting.web_title||"SmartNest"}},created:function(){this.loadOrderInfo(),this.startAutoRefresh()},destroyed:function(){this.stopAutoRefresh()},methods:{loadOrderInfo:async function(){var t=this;try{this.$Model.GetPaymentOrderStatus({order_number:this.orderNumber},function(s){1===s.code?t.orderInfo=s.data||{}:t.$Dialog.Toast(s.msg||t.$t("globalPay.messages.networkError"))})}catch(t){console.error("加载订单信息失败:",t),this.$Dialog.Toast(this.$t("globalPay.messages.networkError"))}},getStatusClass:function(t){return{pending:"status-pending",processing:"status-processing",completed:"status-completed",failed:"status-failed",cancelled:"status-cancelled",expired:"status-expired"}[t]||"status-default"},getStatusIcon:function(t){return{pending:"clock-o",processing:"loading",completed:"success",failed:"close",cancelled:"cross",expired:"warning-o"}[t]||"info-o"},getStatusText:function(t){return this.$t("globalPay.status."+t)||t},getStatusDescription:function(t){return{pending:this.$t("globalPay.tips.processingTip"),processing:this.$t("globalPay.tips.processingTip"),completed:this.$t("globalPay.tips.successTip"),failed:this.$t("globalPay.messages.paymentFailed"),cancelled:"订单已取消",expired:"订单已过期"}[t]||""},copyOrderNumber:function(){this.$Util.CopyText(this.orderInfo.order_number||this.orderNumber),this.$Dialog.Toast(this.$t("dialog[3]"))},formatCurrency:function(t){return this.formatLargeNumber(t)},formatDateTime:function(t){return t?new Date(t).toLocaleString():""},openPaymentUrl:function(){this.orderInfo.payment_info&&this.orderInfo.payment_info.payment_url&&window.open(this.orderInfo.payment_info.payment_url,"_blank")},refreshStatus:async function(){this.isRefreshing=!0,await this.loadOrderInfo(),this.isRefreshing=!1},retryPayment:async function(){this.isRetrying=!0;try{this.$router.push("/user/globalPay")}catch(t){console.error("重试支付失败:",t),this.$Dialog.Toast(this.$t("globalPay.messages.networkError"))}this.isRetrying=!1},cancelOrder:async function(){this.isCancelling=!0;try{this.$Dialog.Toast("订单取消功能待实现")}catch(t){console.error("取消订单失败:",t),this.$Dialog.Toast(this.$t("globalPay.messages.networkError"))}this.isCancelling=!1},startAutoRefresh:function(){var t=this;["pending","processing"].includes(this.orderInfo.status)&&(this.refreshTimer=setInterval(function(){t.loadOrderInfo()},1e4))},stopAutoRefresh:function(){this.refreshTimer&&(clearInterval(this.refreshTimer),this.refreshTimer=null)}},watch:{"orderInfo.status":function(t){["completed","failed","cancelled","expired"].includes(t)?this.stopAutoRefresh():this.startAutoRefresh()}}},n={render:function(){var t=this,s=t.$createElement,e=t._self._c||s;return e("div",{staticClass:"PageBox"},[e("van-nav-bar",{attrs:{fixed:"",border:!1,"left-text":t.webTitle,"left-arrow":""},on:{"click-left":function(s){return t.$router.go(-1)}}}),t._v(" "),e("div",{staticClass:"ScrollBox GlobalPayOrder"},[e("div",{staticClass:"order-status-card"},[e("div",{staticClass:"status-icon",class:t.getStatusClass(t.orderInfo.status)},[e("van-icon",{attrs:{name:t.getStatusIcon(t.orderInfo.status)}})],1),t._v(" "),e("div",{staticClass:"status-text"},[e("div",{staticClass:"status-title"},[t._v(t._s(t.getStatusText(t.orderInfo.status)))]),t._v(" "),e("div",{staticClass:"status-desc"},[t._v(t._s(t.getStatusDescription(t.orderInfo.status)))])])]),t._v(" "),e("div",{staticClass:"order-info-section"},[e("div",{staticClass:"section-title"},[t._v(t._s(t.$t("globalPay.orderNumber")))]),t._v(" "),e("div",{staticClass:"info-list"},[e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v(t._s(t.$t("globalPay.orderNumber")))]),t._v(" "),e("span",{staticClass:"value"},[t._v(t._s(t.orderInfo.order_number||t.orderNumber))]),t._v(" "),e("van-icon",{attrs:{name:"copy"},on:{click:t.copyOrderNumber}})],1),t._v(" "),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v(t._s(t.$t("globalPay.paymentAmount")))]),t._v(" "),e("span",{staticClass:"value amount"},[t._v("\n            "+t._s(t.orderInfo.currency)+" "+t._s(t.formatCurrency(t.orderInfo.amount))+"\n          ")])]),t._v(" "),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v(t._s(t.$t("globalPay.selectPaymentMethod")))]),t._v(" "),e("span",{staticClass:"value"},[t._v(t._s(t.orderInfo.payment_method_name))])]),t._v(" "),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v(t._s(t.$t("globalPay.selectCountry")))]),t._v(" "),e("span",{staticClass:"value"},[t._v("\n            "+t._s(t.orderInfo.country_flag)+" "+t._s(t.orderInfo.country_name)+"\n          ")])]),t._v(" "),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v(t._s(t.$t("common[7]")))]),t._v(" "),e("span",{staticClass:"value"},[t._v(t._s(t.formatDateTime(t.orderInfo.created_at)))])]),t._v(" "),t.orderInfo.completed_at?e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v(t._s(t.$t("task.info[8]")))]),t._v(" "),e("span",{staticClass:"value"},[t._v(t._s(t.formatDateTime(t.orderInfo.completed_at)))])]):t._e()])]),t._v(" "),t.orderInfo.payment_info?e("div",{staticClass:"payment-info-section"},[e("div",{staticClass:"section-title"},[t._v("支付信息")]),t._v(" "),e("div",{staticClass:"payment-details"},[t.orderInfo.payment_info.qr_code?e("div",{staticClass:"qr-code-container"},[e("img",{staticClass:"qr-code",attrs:{src:t.orderInfo.payment_info.qr_code,alt:"QR Code"}}),t._v(" "),e("div",{staticClass:"qr-tips"},[t._v(t._s(t.$t("recharge.tips[1]")))])]):t._e(),t._v(" "),t.orderInfo.payment_info.payment_url?e("div",{staticClass:"payment-url"},[e("van-button",{attrs:{type:"primary",size:"large",block:""},on:{click:t.openPaymentUrl}},[t._v("\n            "+t._s(t.$t("globalPay.buttons.payNow"))+"\n          ")])],1):t._e(),t._v(" "),t.orderInfo.payment_info.instructions?e("div",{staticClass:"payment-instructions"},[e("div",{staticClass:"instructions-title"},[t._v("支付说明")]),t._v(" "),e("div",{staticClass:"instructions-content",domProps:{innerHTML:t._s(t.orderInfo.payment_info.instructions)}})]):t._e()])]):t._e(),t._v(" "),e("div",{staticClass:"action-buttons"},[t.canRetry?e("van-button",{attrs:{type:"warning",size:"large",loading:t.isRetrying},on:{click:t.retryPayment}},[t._v("\n        "+t._s(t.$t("globalPay.buttons.retry"))+"\n      ")]):t._e(),t._v(" "),t.canCancel?e("van-button",{attrs:{type:"default",size:"large",loading:t.isCancelling},on:{click:t.cancelOrder}},[t._v("\n        "+t._s(t.$t("globalPay.buttons.cancel"))+"\n      ")]):t._e(),t._v(" "),e("van-button",{attrs:{type:"primary",size:"large",loading:t.isRefreshing},on:{click:t.refreshStatus}},[t._v("\n        刷新状态\n      ")])],1)])],1)},staticRenderFns:[]};var r=e("VU/8")(a,n,!1,function(t){e("N4j8")},"data-v-6f23e3ed",null);s.default=r.exports},N4j8:function(t,s){}});