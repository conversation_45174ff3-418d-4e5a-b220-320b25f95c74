webpackJsonp([32],{"4KEO":function(t,s,i){"use strict";Object.defineProperty(s,"__esModule",{value:!0});var a={name:"Task",components:{},props:["tabsActive"],data:function(){return{taskType:"",tabsIndex:0,isStore:!1,listData:"",isLoad:!1,isFinished:!1,isRefresh:!1,pageNo:1}},computed:{},watch:{},created:function(){this.tabsIndex=this.tabsActive?Number(this.tabsActive):0,this.InitData.taskclasslist.length&&(this.taskType=this.InitData.taskclasslist.filter(function(t){return 1==t.state})[0].group_id,this.tabsActive&&(this.taskType=this.InitData.taskclasslist.filter(function(t){return 1==t.state})[this.tabsActive].group_id)),this.getListData("init")},mounted:function(){},activated:function(){},destroyed:function(){},methods:{onLoad:function(){this.getListData("load")},changeTabs:function(t){this.taskType=this.InitData.taskclasslist.filter(function(t){return 1==t.state})[t].group_id,this.getListData("init")},openTaskList:function(t,s){var i=this;if(this.UserInfo.vip_level<t){var a=this.$t("task.index[1]",{currVip:this.UserInfo.useridentity,vip:s});this.$Dialog.Confirm(a,function(){i.$router.push("/vip")},this.$t("task.index[2]"))}else this.taskType?this.$router.push("/taskList/"+this.taskType+"/"+t):this.$Dialog.Toast(this.$t("task.index[3]"))},getListData:function(t){var s=this;this.isLoad=!0,this.isRefresh=!1,"load"==t?this.pageNo+=1:(this.pageNo=1,this.isFinished=!1),this.$Model.GetTaskList({group_id:this.taskType,page_no:this.pageNo,is_u:0},function(i){s.$nextTick(function(){s.isLoad=!1}),1==i.code?("load"==t?1==s.pageNo?s.listData=i.info:s.listData=s.listData.concat(i.info):s.listData=i.info,s.pageNo==i.data_total_page?s.isFinished=!0:s.isFinished=!1):(s.listData="",s.isFinished=!0)})},onRefresh:function(){this.getListData("init")},receiveTask:function(t,s){var i=this;localStorage.Token?this.$Model.ReceiveTask(t,function(t){1==t.code&&(s.is_l=1,i.pageNo=0)}):this.$router.push("/login")}}},e={render:function(){var t=this,s=t.$createElement,i=t._self._c||s;return i("div",{staticClass:"Site IndexBox"},[i("div",{staticClass:"ScrollBox"},[i("van-swipe",{staticClass:"customSwipe",staticStyle:{height:"10rem"},attrs:{autoplay:3e3,"indicator-color":"#888"}},t._l(t.InitData.bannerList,function(s,a){return i("van-swipe-item",{key:a},[i("img",{attrs:{src:t.InitData.setting.up_url+s,width:"100%"}})])}),1),t._v(" "),i("van-tabs",{staticClass:"customTabs",attrs:{ellipsis:!1,border:!1,color:"#4087f1","title-active-color":"#fff","title-inactive-color":"#292929","line-width":"0"},on:{change:t.changeTabs},model:{value:t.tabsIndex,callback:function(s){t.tabsIndex=s},expression:"tabsIndex"}},t._l(t.InitData.taskclasslist.filter(function(t){return 1==t.state}),function(s){return i("van-tab",{key:s.group_id,attrs:{title:s.group_name}},[i("van-pull-refresh",{on:{refresh:t.onRefresh},model:{value:t.isRefresh,callback:function(s){t.isRefresh=s},expression:"isRefresh"}},[i("van-list",{class:{Empty:!t.listData.length},attrs:{finished:t.isFinished,"finished-text":t.listData.length?t.$t("vanPull[0]"):t.$t("vanPull[1]")},on:{load:t.onLoad},model:{value:t.isLoad,callback:function(s){t.isLoad=s},expression:"isLoad"}},t._l(t.listData,function(s){return i("van-cell",{key:t.InitData.setting.up_url+s.task_id,staticClass:"TaskItem",attrs:{border:!1,to:"/taskShow/"+s.task_id},scopedSlots:t._u([{key:"title",fn:function(){return[i("div",[i("span",[t._v(t._s(t.$t("task.list[2]"))+":"),i("b",[t._v(t._s(s.surplus_number))])]),t._v(" "),i("span",[t._v("\n                    "+t._s(t.InitData.currency)+"\n                    "),i("em",[t._v(t._s(Number(s.reward_price)))])])]),t._v(" "),i("div",[i("span",[t._v(t._s(t.$t("task.list[3]"))+":"+t._s(s.group_info))]),t._v(" "),i("span",[i("van-button",{attrs:{type:"info",size:"mini",disabled:0!=s.is_l},on:{click:function(i){return i.stopPropagation(),t.receiveTask(s.task_id,s)}}},[t._v(t._s(t.$t("task.list[4]")))])],1)])]},proxy:!0}],null,!0)},[i("div",{staticClass:"icon",attrs:{slot:"icon"},slot:"icon"},[i("h4",[t._v(t._s(s.group_name))]),t._v(" "),i("a",{attrs:{href:"javascript:;"}},[i("img",{attrs:{src:t.InitData.setting.up_url+s.icon}})]),t._v(" "),i("van-tag",{attrs:{type:"primary"}},[t._v(t._s(s.vip_dec))])],1)])}),1)],1)],1)}),1)],1),t._v(" "),i("Footer")],1)},staticRenderFns:[]};var n=i("VU/8")(a,e,!1,function(t){i("iAG8")},"data-v-3278a0dd",null);s.default=n.exports},iAG8:function(t,s){}});