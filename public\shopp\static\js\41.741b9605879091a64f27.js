webpackJsonp([41],{"2QE4":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i={name:"Service",components:{},props:{},data:function(){return{isLoad:!0}},computed:{},watch:{},created:function(){},mounted:function(){var t=this,e=document.getElementById("iframe");e.src=this.$store.state.InitData.setting.activity_url+"?id="+(this.$store.state.UserInfo.userid||""),e.attachEvent?e.attachEvent("onload",function(){t.isLoad=!1}):e.onload=function(){t.isLoad=!1}},activated:function(){},destroyed:function(){},methods:{}},n={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"Site PageBox"},[a("van-nav-bar",{attrs:{fixed:"",border:!1,title:t.$t("Activity[0]"),"left-arrow":""},on:{"click-left":function(e){return t.$router.go(-1)}}}),t._v(" "),t.isLoad?a("van-loading",{staticClass:"DataLoad",attrs:{size:"60px",vertical:""}},[t._v(t._s(t.$t("Activity[1]")))]):t._e(),t._v(" "),a("iframe",{staticStyle:{position:"relative","z-index":"99"},attrs:{id:"iframe",src:"",width:"100%",height:"100%",frameborder:"0"}})],1)},staticRenderFns:[]};var o=a("VU/8")(i,n,!1,function(t){a("doCW")},"data-v-0acd872e",null);e.default=o.exports},doCW:function(t,e){}});