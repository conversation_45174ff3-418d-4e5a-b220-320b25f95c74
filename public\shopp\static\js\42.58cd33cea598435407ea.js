webpackJsonp([42],{"6S5U":function(t,e){},mob8:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n={name:"BindAccount",components:{},props:[],data:function(){return{tabsActive:0,weixinAcc:"",postData:{},douyinImg:[],kuaishouImg:[],accountArr:[],tabsList:[]}},computed:{},watch:{},created:function(){var t=this;this.$parent.navBarTitle=this.$t("bindAccount.default[0]"),this.tabsList=this.InitData.taskclasslist.filter(function(t){return 1==t.bind_status&&1==t.state}),this.accountArr=this.tabsList.flatMap(function(e){return 2==e.bind_type?[t.UserInfo[e.bind_field]?[{url:t.ApiUrl+t.UserInfo[e.bind_field]}]:[]]:t.UserInfo[e.bind_field]||""})},mounted:function(){},activated:function(){},destroyed:function(){},methods:{onSubmit:function(t){this.postData={};var e=this.tabsList[t].bind_field;2==this.tabsList[t].bind_type?this.postData[e]=this.accountArr[t][0].url:this.postData[e]=this.accountArr[t],this.$Model.SetUserInfo(this.postData)},afterRead:function(t){t.status="uploading",t.message=this.$t("upload[0]"),this.uploadImgs(t)},compressImg:function(t){var e=this;this.$Util.CompressImg(t.file.type,t.content,750,function(a){var n=new FormData;n.append("token",localStorage.Token),n.append("type",3),n.append("image",a,t.file.name),e.$Model.UploadImg(n,function(a){1==a.code?(t.message=e.$t("upload[2]"),t.status="success",t.url=a.url):(t.status="failed",t.message=e.$t("upload[3]"))})})},uploadImgs:function(t){var e=this;if(t.length)t.forEach(function(t){if(!t.file.type.match(/image/))return t.status="failed",void(t.message=e.$t("upload[1]"));e.compressImg(t)});else{if(!t.file.type.match(/image/))return t.status="failed",void(t.message=this.$t("upload[1]"));this.compressImg(t)}}}},i={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"PageBox"},[a("div",{staticClass:"ScrollBox"},[a("van-tabs",{staticStyle:{"margin-top":"20px"},attrs:{color:"#4087f1",background:"white","title-active-color":"#fff","title-inactive-color":"#292929",border:!1},model:{value:t.tabsActive,callback:function(e){t.tabsActive=e},expression:"tabsActive"}},t._l(t.tabsList,function(e,n){return a("van-tab",{key:n,attrs:{title:e.group_name}},[a("van-form",{staticClass:"mt15 CustomForm",on:{submit:function(e){return t.onSubmit(n)}}},[1==e.bind_type?a("van-field",{attrs:{label:t.$t("bindAccount.label[2]"),placeholder:t.$t("bindAccount.placeholder",{account:e.group_name}),clearable:"",readonly:!!t.UserInfo[e.bind_field]},model:{value:t.accountArr[n],callback:function(e){t.$set(t.accountArr,n,e)},expression:"accountArr[index]"}}):a("van-field",{attrs:{label:t.$t("bindAccount.label[1]")},scopedSlots:t._u([{key:"input",fn:function(){return[a("van-uploader",{attrs:{"after-read":t.afterRead,"max-count":1,deletable:!t.UserInfo[e.bind_field]},model:{value:t.accountArr[n],callback:function(e){t.$set(t.accountArr,n,e)},expression:"accountArr[index]"}})]},proxy:!0}],null,!0)})],1),t._v(" "),a("div",{staticStyle:{margin:"25px 16px"}},[a("van-button",{staticClass:"loginBtn",staticStyle:{"font-size":"16px"},attrs:{round:"",block:"",type:"danger","native-type":"submit",disabled:!!t.UserInfo[e.bind_field]}},[t._v("\n            "+t._s(t.$t("bindAccount.default[2]"))+"\n          ")])],1)],1)}),1)],1)])},staticRenderFns:[]};var s=a("VU/8")(n,i,!1,function(t){a("6S5U")},"data-v-0901d6a8",null);e.default=s.exports}});