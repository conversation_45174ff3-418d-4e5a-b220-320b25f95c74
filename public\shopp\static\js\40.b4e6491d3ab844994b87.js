webpackJsonp([40],{"/KTk":function(t,e){},"8H+I":function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s={name:"newLc",components:{},props:[],data:function(){return{showHeader:!1,showPassWord:!1,showPayWord:!1,radioHeader:[],postData:{},active:0,money:"",all:0,lv:0,day:0,flag:!0}},computed:{},watch:{money:function(){return this.all=this.money*this.lv*this.day}},created:function(){this.$parent.navBarTitle=this.$t("newLc[9]"),console.log(this.$parent),this.list()},mounted:function(){},activated:function(){},destroyed:function(){},methods:{sub:function(){var t=this;if(""!=this.money&&0!=this.active){var e={userid:JSON.parse(localStorage.getItem("UserInfo")).userid,yuebaoid:this.active,money:this.money};this.$Model.newLcTj(e,function(e){if(200==e.errorCode)return t.$Dialog.Toast(e.successMsg),t.active=0,t.flag=!0,t.money="",void t.$router.push("/user/");t.$Dialog.Toast(e.errorMsg)})}else this.$Dialog.Toast(this.$t("newLc[10]"))},inputa:function(){0==this.active&&this.$Dialog.Toast(this.$t("newLc[11]"))},add:function(t){this.flag=!1,this.active=t.id,this.lv=t.lilv,this.day=t.time,this.all=this.money*this.lv*this.day},list:function(){var t=this;this.$Model.newLc({},function(e){t.radioHeader=e.info})}}},n={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"PageBox"},[i("div",{staticClass:"one"},[i("h5",[i("i",[t._v(t._s(t.$t("newLc[0]")))]),i("span")]),t._v(" "),i("p",{staticStyle:{color:"#000"}},[i("img",{attrs:{src:"static/images/nav-009.58e733bf.png",alt:"",srcset:""}}),t._v(t._s(t.$t("newLc[1]")))])]),t._v(" "),i("div",{staticClass:"eee"}),t._v(" "),i("div",{staticClass:"one two"},[i("h4",[t._v(t._s(t.$t("newLc[2]")))]),t._v(" "),i("ul",{staticClass:"ulC"},t._l(t.radioHeader,function(e,s){return i("li",{key:s,class:{active:t.active==e.id},on:{click:function(i){return t.add(e)}}},[i("span",[t._v(t._s(e.title)+t._s(e.tiem)+t._s(t.$t("newLc[3]")))]),i("i",[t._v(t._s(t.$t("newLc[4]"))+t._s(100*e.lilv)+"%")])])}),0)]),t._v(" "),i("div",{staticClass:"eee"}),t._v(" "),i("div",{staticClass:"one two"},[i("h4",[t._v(t._s(t.$t("newLc[5]")))]),t._v(" "),i("div",{staticClass:"three",on:{click:function(e){return t.inputa()}}},[i("i",[t._v(t._s(t.$Currency.getSymbol()))]),t._v(" "),i("input",{directives:[{name:"model",rawName:"v-model.trim",value:t.money,expression:"money",modifiers:{trim:!0}}],staticStyle:{border:"0",flex:"1",background:"transparent"},attrs:{type:"number",maxlength:"8",disabled:t.flag,placeholder:t.$t("newLc[6]")},domProps:{value:t.money},on:{input:function(e){e.target.composing||(t.money=e.target.value.trim())},blur:function(e){return t.$forceUpdate()}}}),i("span",[t._v("("+t._s(t.$t("newLc[7]"))+t._s(t.all.toFixed(2))+")")])])]),t._v(" "),i("div",{staticClass:"eee"}),t._v(" "),i("div",{staticClass:"btn",on:{click:function(e){return t.sub()}}},[t._v(t._s(t.$t("newLc[8]")))])])},staticRenderFns:[]};var a=i("VU/8")(s,n,!1,function(t){i("/KTk")},"data-v-0ea2a57a",null);e.default=a.exports}});