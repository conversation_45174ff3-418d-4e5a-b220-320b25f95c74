webpackJsonp([33],{Bwv1:function(e,t){},LsrL:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s={name:"Info",components:{},props:[],data:function(){return{showHeader:!1,showPassWord:!1,showPayWord:!1,radioHeader:"",postData:{}}},computed:{},watch:{},created:function(){this.$parent.navBarTitle=this.$t("userInfo.default[0]")},mounted:function(){},activated:function(){},destroyed:function(){},methods:{selectHeader:function(e){this.postData.header="head_"+e+".png",this.setUserInfo()},setUserInfo:function(){var e=this;this.$Model.SetUserInfo(this.postData,function(t){1==t.code&&(e.showHeader&&(e.showHeader=!1),e.showPassWord&&(e.showPassWord=!1),e.showPayWord&&(e.showPayWord=!1),e.postData={})})},clearCache:function(){localStorage.clear(),this.$Model.GetBackData(),this.$router.push("/login")}}},o={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"PageBox"},[a("div",{staticClass:"ScrollBox"},[a("div",{staticClass:"CustomCell"},[a("van-cell",{attrs:{size:"large",icon:"./static/icon/info_001.png",title:e.$t("userInfo.default[1]"),center:"","is-link":""},on:{click:function(t){e.showHeader=!0}}},[e.UserInfo&&e.UserInfo.header?a("img",{staticStyle:{"border-radius":"50%"},attrs:{src:"./static/head/"+e.UserInfo.header,height:"45"}}):a("img",{staticStyle:{"border-radius":"50%"},attrs:{src:"/static/head/head_1.png",height:"45"}})]),e._v(" "),a("van-cell",{attrs:{size:"large",icon:"./static/icon/info_002.png",title:e.$t("userInfo.default[2]"),center:"",value:e.UserInfo.phone}}),e._v(" "),a("van-cell",{attrs:{size:"large",icon:"./static/icon/info_003.png",title:e.$t("usdt[0]"),center:"",value:e.$t("userInfo.default[8]"),to:"bankCard","is-link":""}}),e._v(" "),a("van-cell",{attrs:{size:"large",icon:"./static/icon/info_005.png",title:e.$t("userInfo.default[5]"),center:"",value:e.$t("userInfo.default[8]"),to:"set/info","is-link":""}}),e._v(" "),a("van-cell",{attrs:{size:"large",icon:"./static/icon/info_006.png",title:e.$t("userInfo.default[6]"),center:"",value:e.$t("userInfo.default[8]"),"is-link":""},on:{click:function(t){e.showPassWord=!0}}}),e._v(" "),a("van-cell",{attrs:{size:"large",icon:"./static/icon/info_007.png",title:e.$t("userInfo.default[7]"),center:"",value:e.$t("userInfo.default[8]"),"is-link":""},on:{click:function(t){e.showPayWord=!0}}}),e._v(" "),a("van-cell",{staticClass:"Cache",attrs:{size:"large",icon:"delete",title:e.$t("userInfo.default[13]"),center:"","is-link":""},on:{click:e.clearCache}})],1)]),e._v(" "),a("van-action-sheet",{staticClass:"DrawPopup",attrs:{title:e.$t("userInfo.default[9]"),"close-on-popstate":""},model:{value:e.showHeader,callback:function(t){e.showHeader=t},expression:"showHeader"}},[a("van-radio-group",{model:{value:e.radioHeader,callback:function(t){e.radioHeader=t},expression:"radioHeader"}},[a("van-grid",{attrs:{clickable:"","icon-size":"45","column-num":"5"}},e._l(10,function(t){return a("van-grid-item",{key:t,attrs:{icon:"./static/head/head_"+t+".png"},on:{click:function(a){return e.selectHeader(t)}}})}),1)],1)],1),e._v(" "),a("van-action-sheet",{staticClass:"DrawPopup",attrs:{title:e.$t("userInfo.default[10]"),"close-on-popstate":""},model:{value:e.showPassWord,callback:function(t){e.showPassWord=t},expression:"showPassWord"}},[a("div",{staticClass:"DrawPopupBox"},[a("van-field",{attrs:{type:"password",label:e.$t("userInfo.label[0]"),placeholder:e.$t("userInfo.placeholder[0]"),clearable:"",size:"large"},model:{value:e.postData.o_password,callback:function(t){e.$set(e.postData,"o_password","string"==typeof t?t.trim():t)},expression:"postData.o_password"}}),e._v(" "),a("van-field",{attrs:{type:"password",label:e.$t("userInfo.label[1]"),placeholder:e.$t("userInfo.placeholder[1]"),clearable:"",size:"large"},model:{value:e.postData.n_password,callback:function(t){e.$set(e.postData,"n_password","string"==typeof t?t.trim():t)},expression:"postData.n_password"}}),e._v(" "),a("van-field",{attrs:{type:"password",label:e.$t("userInfo.label[2]"),placeholder:e.$t("userInfo.placeholder[2]"),clearable:"",size:"large"},model:{value:e.postData.r_password,callback:function(t){e.$set(e.postData,"r_password","string"==typeof t?t.trim():t)},expression:"postData.r_password"}})],1),e._v(" "),a("div",{staticStyle:{padding:"16px"}},[a("van-button",{staticClass:"loginBtn",staticStyle:{"font-size":"16px"},attrs:{type:"danger",block:""},on:{click:e.setUserInfo}},[e._v(e._s(e.$t("userInfo.default[12]")))])],1)]),e._v(" "),a("van-action-sheet",{staticClass:"DrawPopup",attrs:{title:e.$t("userInfo.default[11]"),"close-on-popstate":""},model:{value:e.showPayWord,callback:function(t){e.showPayWord=t},expression:"showPayWord"}},[a("div",{staticClass:"DrawPopupBox"},[1==e.UserInfo.is_fund_password?a("van-field",{attrs:{type:"password",label:e.$t("userInfo.label[3]"),placeholder:e.$t("userInfo.placeholder[3]"),clearable:"",size:"large"},model:{value:e.postData.o_payword,callback:function(t){e.$set(e.postData,"o_payword","string"==typeof t?t.trim():t)},expression:"postData.o_payword"}}):e._e(),e._v(" "),a("van-field",{attrs:{type:"password",label:e.$t("userInfo.label[4]"),placeholder:e.$t("userInfo.placeholder[4]"),clearable:"",size:"large"},model:{value:e.postData.n_payword,callback:function(t){e.$set(e.postData,"n_payword","string"==typeof t?t.trim():t)},expression:"postData.n_payword"}}),e._v(" "),a("van-field",{attrs:{type:"password",label:e.$t("userInfo.label[5]"),placeholder:e.$t("userInfo.placeholder[5]"),clearable:"",size:"large"},model:{value:e.postData.r_payword,callback:function(t){e.$set(e.postData,"r_payword","string"==typeof t?t.trim():t)},expression:"postData.r_payword"}})],1),e._v(" "),a("div",{staticStyle:{padding:"16px"}},[a("van-button",{staticClass:"loginBtn",staticStyle:{"font-size":"16px"},attrs:{type:"danger",block:""},on:{click:e.setUserInfo}},[e._v(e._s(e.$t("userInfo.default[12]")))])],1)])],1)},staticRenderFns:[]};var r=a("VU/8")(s,o,!1,function(e){a("Bwv1")},"data-v-2efef519",null);t.default=r.exports}});