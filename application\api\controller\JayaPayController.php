<?php
namespace app\api\controller;

use think\Controller;

// 引入JayaPay签名工具类
require_once __DIR__ . '/../../common/library/JayaPaySignUtil.php';

class JayaPayController extends Controller
{
    //初始化方法
    protected function initialize()
    {
        parent::initialize();
        header('Access-Control-Allow-Origin:*');
    }

    /**
     * 获取支持的支付方式
     */
    public function getPaymentMethods()
    {
        $lang = (input('post.lang')) ? input('post.lang') : 'id';
        $texts = $this->getTexts($lang);

        try {
            // 直接读取配置文件，修复路径问题
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (file_exists($configPath)) {
                $paymentConfig = include($configPath);
                $jayaPayConfig = $paymentConfig['jaya_pay'] ?? [];
            } else {
                $jayaPayConfig = [];
            }
            $paymentMethods = [];

            // 获取支持的支付方式（使用新的pay_types配置）
            $payTypes = $jayaPayConfig['pay_types'] ?? [];
            foreach ($payTypes as $code => $method) {
                if ($method['enabled']) {
                    $paymentMethods[] = [
                        'code' => $code,
                        'name' => $method['name'],
                        'type' => $method['type'],
                        'enabled' => $method['enabled'],
                        'requires_bank_code' => $method['requires_bank_code'] ?? false // 添加是否需要bank_code字段
                    ];
                }
            }

            return json([
                'code' => 1,
                'msg' => $texts['messages']['success'],
                'data' => [
                    'payment_methods' => $paymentMethods,
                    'countries' => $jayaPayConfig['countries'],
                    'bank_codes' => $jayaPayConfig['bank_codes']
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => $texts['messages']['get_payment_methods_failed'] . ': ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 创建支付订单
     */
    public function createOrder()
    {
        $param = input('post.');
        $lang = (input('post.lang')) ? input('post.lang') : 'id';
        $texts = $this->getTexts($lang);

        try {
            // 参数验证 - JayaPay不需要pay_type参数
            $required = ['token', 'recharge_id', 'amount'];
            foreach ($required as $field) {
                if (!isset($param[$field]) || empty($param[$field])) {
                    return json(['code' => 0, 'msg' => $texts['messages']['param_required'] . ": {$field}"]);
                }
            }

            // 验证用户token
            $token = $param['token'];
            $userArr = explode(',', auth_code($token, 'DECODE'));
            $uid = $userArr[0] ?? 0;
            $username = $userArr[1] ?? '';

            if (empty($uid)) {
                return json(['code' => 0, 'msg' => $texts['messages']['invalid_login']]);
            }

            // 验证用户状态
            $user = model('Users')->where('id', $uid)->find();
            if (!$user || $user['state'] != 1) {
                return json(['code' => 0, 'msg' => $texts['messages']['user_disabled']]);
            }

            // 验证充值渠道
            $rechargeType = model('RechangeType')->where(['id' => $param['recharge_id'], 'mode' => 'jaya_pay', 'state' => 1])->find();
            if (!$rechargeType) {
                return json(['code' => 0, 'msg' => $texts['messages']['channel_not_found']]);
            }

            // 验证金额
            $amount = floatval($param['amount']);
            if ($amount < $rechargeType['minPrice'] || $amount > $rechargeType['maxPrice']) {
                return json(['code' => 0, 'msg' => $texts['messages']['amount_out_of_range']]);
            }

            // 计算到账金额
            $fee = $amount * $rechargeType['fee'] / 100;
            $daozhangMoney = $amount - $fee;

            // 生成订单号
            $orderNo = \JayaPaySignUtil::generateOrderNo('JP');

            // 创建充值记录
            $rechargeData = [
                'uid' => $uid,
                'order_number' => $orderNo,
                'money' => $amount,
                'daozhang_money' => $daozhangMoney,
                'fee' => $fee,
                'type' => $param['recharge_id'],
                'add_time' => time(),
                'state' => 0,
                'remarks' => 'JayaPay充值订单'
            ];

            $rechargeId = model('UserRecharge')->insertGetId($rechargeData);
            
            if (!$rechargeId) {
                return json(['code' => 0, 'msg' => $texts['messages']['order_create_failed']]);
            }

            // 调用第三方支付API
            $thirdPayService = new \app\common\service\ThirdPayService();
            $orderData = [
                'recharge_id' => $param['recharge_id'],
                'order_no' => $orderNo,
                'amount' => $amount,
                'notify_url' => $this->getNotifyUrl($param['recharge_id'])
            ];

            // JayaPay不需要pay_type，如果传递了就使用
            if (!empty($param['pay_type'])) {
                $orderData['pay_type'] = $param['pay_type'];
            }

            // 如果前端传递了bank_code，添加到订单数据中
            if (!empty($param['bank_code'])) {
                $orderData['bank_code'] = $param['bank_code'];
                \think\facade\Log::info('JayaPay received bank_code: ' . $param['bank_code']);
            }

            $result = $thirdPayService->createOrder($orderData);

            if (isset($result['code']) && $result['code'] == 1) {
                // 检查是否有支付URL
                if (!isset($result['data']) || !is_array($result['data']) || !isset($result['data']['pay_url'])) {
                    // 删除失败的订单
                    model('UserRecharge')->where('id', $rechargeId)->delete();
                    return json([
                        'code' => 0,
                        'msg' => $texts['messages']['payment_url_missing']
                    ]);
                }

                // 更新充值记录
                model('UserRecharge')->where('id', $rechargeId)->update([
                    'submitUrl' => $result['data']['pay_url']
                ]);

                return json([
                    'code' => 1,
                    'msg' => $texts['messages']['order_created'],
                    'data' => [
                        'order_no' => $orderNo,
                        'pay_url' => $result['data']['pay_url'],
                        'amount' => $amount,
                        'recharge_id' => $rechargeId,
                        'platform_order_no' => $result['data']['platform_order_no'] ?? ''
                    ]
                ]);
            } else {
                // 删除失败的订单
                model('UserRecharge')->where('id', $rechargeId)->delete();
                return json([
                    'code' => 0,
                    'msg' => $result['msg'] ?? $texts['messages']['order_create_failed']
                ]);
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('JayaPay createOrder error: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => $texts['messages']['system_error'] . ': ' . $e->getMessage()]);
        }
    }

    /**
     * 获取回调地址
     */
    private function getNotifyUrl($rechargeId)
    {
        // 优先从配置文件获取统一回调URL
        $notifyUrl = $this->getNotifyUrlFromConfig();

        if ($notifyUrl) {
            return $notifyUrl;
        }

        // 兜底方案：从全局配置获取默认域名生成统一充值回调地址
        try {
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (file_exists($configPath)) {
                $config = include $configPath;
                $defaultDomain = $config['global']['default_notify_domain'] ?? 'http://localhost';
                return rtrim($defaultDomain, '/') . '/api/transaction/unifiedCallback';
            }
        } catch (\Exception $e) {
            // 配置文件读取失败，使用最终兜底方案
        }

        // 最终兜底方案：自动检测当前服务器信息
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] ?? 'localhost';

        return $protocol . '://' . $host . '/api/transaction/unifiedCallback';
    }

    /**
     * 从配置文件获取回调URL
     */
    private function getNotifyUrlFromConfig()
    {
        try {
            // 直接读取配置文件
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (!file_exists($configPath)) {
                return null;
            }

            $paymentConfig = include($configPath);

            // 直接使用全局统一充值回调地址
            return $paymentConfig['global']['unified_recharge_callback_url'] ?? null;

        } catch (\Exception $e) {
            \think\facade\Log::error("Failed to get notify URL from config: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 支付回调处理
     */
    public function notify()
    {
        try {
            $params = input('post.');
            
            // 记录回调日志
            \think\facade\Log::info('JayaPay notify: ' . json_encode($params));

            // 调用第三方支付服务处理回调
            $thirdPayService = new \app\common\service\ThirdPayService();
            $result = $thirdPayService->handleNotify($params);

            if ($result) {
                echo 'SUCCESS';
            } else {
                echo 'fail';
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('JayaPay notify error: ' . $e->getMessage());
            echo 'fail';
        }
    }

    /**
     * 获取多语言文本
     */
    private function getTexts($lang)
    {
        $texts = [];
        
        if ($lang == 'cn' || $lang == 'zh') {
            $texts['messages'] = [
                'success' => '成功',
                'param_required' => '缺少必要参数',
                'invalid_login' => '用户登录信息无效',
                'user_disabled' => '用户账户已被禁用',
                'channel_not_found' => '充值渠道不存在或已关闭',
                'amount_out_of_range' => '充值金额超出限制范围',
                'order_create_failed' => '订单创建失败',
                'order_created' => '订单创建成功',
                'payment_url_missing' => '支付链接获取失败',
                'system_error' => '系统错误',
                'get_payment_methods_failed' => '获取支付方式失败'
            ];
        } elseif ($lang == 'en') {
            $texts['messages'] = [
                'success' => 'Success',
                'param_required' => 'Required parameter missing',
                'invalid_login' => 'Invalid user login information',
                'user_disabled' => 'User account is disabled',
                'channel_not_found' => 'Recharge channel not found or disabled',
                'amount_out_of_range' => 'Amount out of allowed range',
                'order_create_failed' => 'Order creation failed',
                'order_created' => 'Order created successfully',
                'payment_url_missing' => 'Payment URL not available',
                'system_error' => 'System error',
                'get_payment_methods_failed' => 'Failed to get payment methods'
            ];
        } else { // 默认印尼语
            $texts['messages'] = [
                'success' => 'Berhasil',
                'param_required' => 'Parameter yang diperlukan hilang',
                'invalid_login' => 'Informasi login pengguna tidak valid',
                'user_disabled' => 'Akun pengguna dinonaktifkan',
                'channel_not_found' => 'Saluran pengisian tidak ditemukan atau dinonaktifkan',
                'amount_out_of_range' => 'Jumlah di luar rentang yang diizinkan',
                'order_create_failed' => 'Pembuatan pesanan gagal',
                'order_created' => 'Pesanan berhasil dibuat',
                'payment_url_missing' => 'URL pembayaran tidak tersedia',
                'system_error' => 'Kesalahan sistem',
                'get_payment_methods_failed' => 'Gagal mendapatkan metode pembayaran'
            ];
        }

        return $texts;
    }
}
