<?php
namespace app\api\controller;

use app\api\controller\BaseController;
use app\common\service\WithdrawalService;
use think\facade\Log;

class WithdrawalController extends BaseController
{
    /**
     * 获取可用的代付渠道列表
     */
    public function getChannels()
    {
        try {
            $channels = model('WithdrawalChannel')->getEnabledChannels();
            
            $result = [];
            foreach ($channels as $channel) {
                $result[] = [
                    'id' => $channel['id'],
                    'name' => $channel['name'],
                    'code' => $channel['code'],
                    'mode' => $channel['mode'],
                    'min_amount' => $channel['min_amount'],
                    'max_amount' => $channel['max_amount'],
                    'fee_rate' => $channel['fee_rate']
                ];
            }
            
            return json(['code' => 1, 'data' => $result]);
            
        } catch (\Exception $e) {
            Log::error('Get withdrawal channels error: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '获取代付渠道失败']);
        }
    }

    /**
     * 统一代付接口
     */
    public function processWithdrawal()
    {
        try {
            $param = input('post.');
            
            // 基础参数验证
            $required = ['withdrawal_id', 'channel_id'];
            foreach ($required as $field) {
                if (!isset($param[$field]) || empty($param[$field])) {
                    return json(['code' => 0, 'msg' => "缺少必要参数: {$field}"]);
                }
            }

            // 获取提现记录
            $withdrawal = model('UserWithdrawals')->where('id', $param['withdrawal_id'])->find();
            if (!$withdrawal) {
                return json(['code' => 0, 'msg' => '提现记录不存在']);
            }

            // 检查提现状态
            if ($withdrawal['state'] != 2) { // 假设2是待代付状态
                return json(['code' => 0, 'msg' => '提现状态不允许代付']);
            }

            // 调用代付服务
            $withdrawalService = new WithdrawalService();
            $result = $withdrawalService->processWithdrawal($withdrawal, $param['channel_id']);

            if ($result['code'] == 1) {
                // 更新提现记录状态为代付中
                model('UserWithdrawals')->where('id', $param['withdrawal_id'])
                    ->update([
                        'state' => 3, // 代付中状态
                        'channel_id' => $param['channel_id'],
                        'process_time' => time()
                    ]);

                Log::info('Withdrawal processing started: ' . json_encode([
                    'withdrawal_id' => $param['withdrawal_id'],
                    'channel_id' => $param['channel_id']
                ]));
            } else {
                // 代付失败，获取渠道信息并更新订单备注
                $channel = model('WithdrawalChannel')->where('id', $param['channel_id'])->find();
                $channelName = $channel ? $channel['name'] : '未知渠道';

                $failureRemark = "[" . date('Y-m-d H:i:s') . "] {$channelName}代付失败: " . $result['msg'];
                model('UserWithdrawals')->where('id', $param['withdrawal_id'])
                    ->update([
                        'remarks' => $failureRemark,
                        'process_time' => time()
                    ]);

                Log::error('API withdrawal failed: ' . json_encode([
                    'withdrawal_id' => $param['withdrawal_id'],
                    'channel_id' => $param['channel_id'],
                    'error' => $result['msg']
                ]));
            }

            return json($result);

        } catch (\Exception $e) {
            Log::error('Process withdrawal error: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '代付处理异常：' . $e->getMessage()]);
        }
    }

    /**
     * 注意：回调处理已统一到 TransactionController::unifiedWithdrawalCallback()
     * 这里保留方法作为兼容，但建议使用统一回调接口
     */

    /**
     * 处理代付成功
     */
    private function handleWithdrawalSuccess($withdrawalId)
    {
        try {
            // 更新提现状态为成功
            model('UserWithdrawals')->where('id', $withdrawalId)->update([
                'state' => 1, // 成功状态
                'success_time' => time()
            ]);

            Log::info('Withdrawal success handled: ' . $withdrawalId);

        } catch (\Exception $e) {
            Log::error('Handle withdrawal success error: ' . $e->getMessage());
        }
    }

    /**
     * 处理代付失败
     */
    private function handleWithdrawalFailed($withdrawalId, $reason)
    {
        try {
            // 获取提现记录
            $withdrawal = model('UserWithdrawals')->where('id', $withdrawalId)->find();
            if (!$withdrawal) {
                return;
            }

            // 更新提现状态为失败
            model('UserWithdrawals')->where('id', $withdrawalId)->update([
                'state' => 4, // 失败状态
                'fail_reason' => $reason,
                'fail_time' => time()
            ]);

            // 退回用户余额
            model('UserTotal')->where('uid', $withdrawal['uid'])
                ->setInc('balance', $withdrawal['price'] + $withdrawal['fee']);

            // 记录资金流水
            model('UserTransaction')->insert([
                'uid' => $withdrawal['uid'],
                'type' => 2, // 提现退回
                'money' => $withdrawal['price'] + $withdrawal['fee'],
                'balance' => model('UserTotal')->where('uid', $withdrawal['uid'])->value('balance'),
                'remarks' => '代付失败，退回提现金额：' . $reason,
                'time' => time()
            ]);

            Log::info('Withdrawal failed handled: ' . $withdrawalId . ', reason: ' . $reason);

        } catch (\Exception $e) {
            Log::error('Handle withdrawal failed error: ' . $e->getMessage());
        }
    }

    /**
     * 验证WatchPay签名
     */
    private function verifyWatchPaySign($params, $secretKey)
    {
        $sign = $params['sign'] ?? '';
        unset($params['sign']);
        
        ksort($params);
        
        $signStr = '';
        foreach ($params as $key => $value) {
            if ($value !== '' && $value !== null) {
                $signStr .= $key . '=' . $value . '&';
            }
        }
        $signStr .= 'key=' . $secretKey;
        
        return md5($signStr) === $sign;
    }

    /**
     * 验证JayaPay签名
     */
    private function verifyJayaPaySign($params, $secretKey)
    {
        $sign = $params['sign'] ?? '';
        unset($params['sign']);
        
        ksort($params);
        
        $signStr = '';
        foreach ($params as $key => $value) {
            if ($value !== '' && $value !== null) {
                $signStr .= $key . '=' . $value . '&';
            }
        }
        $signStr .= 'key=' . $secretKey;
        
        return strtoupper(md5($signStr)) === $sign;
    }
}
