webpackJsonp([35],{FZxq:function(t,e){},LbE0:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"Site PageBox"},[n("van-nav-bar",{attrs:{fixed:"",border:!1,title:t.$t("help[0]"),"left-arrow":""},on:{"click-left":function(e){return t.$router.go(-1)}}}),t._v(" "),n("div",{staticClass:"ScrollBox"},[t._l(t.InitData.helpList,function(e){return n("van-cell",{key:e.id,attrs:{size:"large",title:e.title,"is-link":""},on:{click:function(n){return t.openShow(e)}}})}),t._v(" "),t.InitData.helpList.length?t._e():n("van-empty",{attrs:{description:t.$t("help[1]")}})],2),t._v(" "),n("van-popup",{staticStyle:{width:"100%",height:"100%","background-color":"#0e1526"},attrs:{position:"bottom",closeable:"","close-on-popstate":""},model:{value:t.showCon,callback:function(e){t.showCon=e},expression:"showCon"}},[n("div",{staticClass:"ScrollBox",staticStyle:{padding:"16px 20px"}},[n("h3",{staticStyle:{"text-align":"center","margin-bottom":"20px"}},[t._v(t._s(t.infoData.title))]),t._v(" "),n("div",{staticClass:"Content",staticStyle:{"text-align":"justify"},domProps:{innerHTML:t._s(t.infoData.content)}})])])],1)},staticRenderFns:[]};var i=n("VU/8")({name:"Help",components:{},props:[],data:function(){return{showCon:!1,infoData:""}},computed:{},watch:{},created:function(){},mounted:function(){},activated:function(){},destroyed:function(){},methods:{openShow:function(t){this.showCon=!0,this.infoData=t}}},o,!1,function(t){n("FZxq")},"data-v-2b487b73",null);e.default=i.exports}});