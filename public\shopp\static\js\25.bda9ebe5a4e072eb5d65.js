webpackJsonp([25],{QHWA:function(t,e){},R6Y6:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s={name:"Vip",components:{},props:[],data:function(){return{currentCardIndex:0}},computed:{gradeList:function(){return this.InitData&&this.InitData.UserGradeList?(console.log("计算属性 gradeList:",this.InitData.UserGradeList),this.InitData.UserGradeList):[]},currentGradeInfo:function(){return this.gradeList.length>0?this.gradeList[this.currentCardIndex]||this.gradeList[0]:null},currentUserGrade:function(){return this.UserInfo&&this.UserInfo.vip_level?this.UserInfo.vip_level:0},isVipLocked:function(){return this.currentGradeInfo&&1==this.currentGradeInfo.is_locked},shouldShowButton:function(){if(!this.currentGradeInfo)return!1;var t=this.currentGradeInfo.grade,e=this.currentUserGrade;return(1!==e||1!==t)&&(t===e?this.isVipExpired():t>e)}},watch:{},created:function(){var t=this;this.$Model.GetUserInfo(),this.VipList?this.setDefaultCardIndex():this.$Model.GetBackData(function(e){console.log("VIP页面获取到的数据:",e),console.log("UserGradeList:",e.UserGradeList),t.setDefaultCardIndex()}),console.log("VIP页面初始化"),console.log("InitData:",this.InitData),console.log("UserGradeList:",this.InitData.UserGradeList)},mounted:function(){this.setDefaultCardIndex()},activated:function(){this.setDefaultCardIndex()},destroyed:function(){this.$toast.clear()},methods:{getFilteredPrivilegeDescription:function(){if(!this.currentGradeInfo||!this.currentGradeInfo.privilege_description)return"";var t=this.currentGradeInfo.privilege_description;return t=(t=(t=(t=(t=t.replace(/text-wrap-mode:\s*nowrap;?/gi,"")).replace(/white-space:\s*nowrap;?/gi,"")).replace(/word-break:\s*keep-all;?/gi,"")).replace(/style="\s*"/gi,"")).replace(/style=''\s*/gi,"")},getButtonText:function(){if(!this.currentGradeInfo)return this.$t("vip.buttons.payNow");var t=this.currentGradeInfo.grade,e=this.currentUserGrade;return t===e?this.isVipExpired()?this.$t("vip.buttons.renewNow"):this.$t("vip.buttons.payNow"):t>e?this.$t("vip.buttons.buyNow"):this.$t("vip.buttons.payNow")},setDefaultCardIndex:function(){var t=this;if(0!==this.gradeList.length){var e=this.$route.query.targetLevel;if(e){var i=parseInt(e),s=this.gradeList.findIndex(function(t){return t.grade===i});if(-1!==s)return this.currentCardIndex=s,console.log("设置卡片索引为目标等级:",s,"等级:",i),void this.$nextTick(function(){t.$refs.vipSwipe&&t.$refs.vipSwipe.swipeTo(s)})}if(this.UserInfo&&this.UserInfo.vip_level){var n=this.UserInfo.vip_level,r=this.gradeList.findIndex(function(t){return t.grade===n});-1!==r&&(this.currentCardIndex=r,console.log("设置默认卡片索引为用户当前等级:",r,"等级:",n),this.$nextTick(function(){t.$refs.vipSwipe&&t.$refs.vipSwipe.swipeTo(r)}))}}},onCardChange:function(t){console.log("卡片切换到索引:",t),this.currentCardIndex=t,console.log("当前显示的会员信息:",this.currentGradeInfo)},submitBuy:function(t,e,i){var s=this;if(this.UserInfo)if(this.isVipLocked)this.$toast.fail(this.$t("vip.vipLocked")||"该VIP等级已被锁定，无法购买");else{var n=this.$t("vip.dialog[0]",{currency:this.$Currency.getSymbol(),amount:t,name:i});this.UserInfo.vip_level>e?this.$toast.fail(this.$t("vip.dialog[1]",{currname:this.UserInfo.useridentity,name:i})):(this.UserInfo.vip_level==e&&(n=this.$t("vip.dialog[2]",{currency:this.$Currency.getSymbol(),amount:t,name:i})),this.$Dialog.Confirm(n,function(){s.$Model.BuyVip({grade:e},function(e){1==e.code&&s.$Model.GetUserInfo(),2==e.code&&s.$router.push("/user/watchPay?amount="+Number(e.amount||t))})}))}else this.$router.push("/login")},getVipCardClass:function(t){var e=parseInt(t)||1;return"vip-level-"+Math.min(Math.max(e,1),9)},formatDate:function(t){if(!t)return"";try{var e=new Date(t);return e.getFullYear()+"-"+String(e.getMonth()+1).padStart(2,"0")+"-"+String(e.getDate()).padStart(2,"0")}catch(e){return console.error("日期格式化错误:",e),t}},isVipTimeValid:function(){if(!this.UserInfo)return!1;var t=this.UserInfo.stime&&""!==this.UserInfo.stime.trim(),e=this.UserInfo.etime&&""!==this.UserInfo.etime.trim();return console.log("VIP时间有效性检查:",{stime:this.UserInfo.stime,etime:this.UserInfo.etime,hasValidStime:t,hasValidEtime:e,isValid:t&&e}),t&&e},isVipExpired:function(){if(!this.isVipTimeValid())return console.log("VIP续费检查: 时间字段无效，显示为已过期，可以续费"),!0;try{var t=new Date(this.UserInfo.etime),e=new Date,i=new Date(t);i.setDate(t.getDate()-3);var s=e>=i;return console.log("VIP续费检查:",{expireDate:this.formatDate(this.UserInfo.etime),currentDate:this.formatDate(e.toISOString()),threeDaysBeforeExpire:this.formatDate(i.toISOString()),canRenew:s}),s}catch(t){return console.error("VIP到期时间判断错误:",t),!0}},getLotteryBenefitNumber:function(){if(!this.currentGradeInfo)return 3;return void 0!==this.currentGradeInfo.number?3:2},getCommissionBenefitNumber:function(){if(!this.currentGradeInfo)return 2;var t=2;return void 0!==this.currentGradeInfo.number&&t++,void 0!==this.currentGradeInfo.daily_turntable_times&&t++,t}}},n={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"Site PageBox no-padding"},[i("div",{staticClass:"ScrollBox"},[i("div",{staticClass:"vip-page"},[i("div",{staticClass:"vip-cards-container"},[i("van-swipe",{ref:"vipSwipe",staticClass:"vip-swipe",attrs:{autoplay:0,"show-indicators":!1,"initial-swipe":t.currentCardIndex},on:{change:t.onCardChange}},t._l(t.gradeList,function(e,s){return i("van-swipe-item",{key:e.grade},[i("div",{staticClass:"vip-card"},[i("div",{staticClass:"card-background",class:t.getVipCardClass(e.grade)},[i("div",{staticClass:"card-content"},[i("div",{staticClass:"agent-title"},[t._v(t._s(e.name))])]),t._v(" "),t.UserInfo&&t.UserInfo.vip_level===e.grade?i("div",{staticClass:"vip-expire-center"},[t.isVipTimeValid()?i("span",[t._v("\n                "+t._s(t.$t("vip.expireTime"))+": "+t._s(t.formatDate(t.UserInfo.etime))+"\n              ")]):i("span",{staticClass:"vip-expired"},[t._v("\n                "+t._s(t.$t("vip.expired"))+"\n              ")])]):t._e(),t._v(" "),i("div",{staticClass:"card-footer"},[i("div",{staticClass:"price-tag"},[t._v("\n                "+t._s(t.$Currency.formatAmount(e.amount))+"\n              ")])])])])])}),1)],1),t._v(" "),i("div",{staticClass:"main-content"},[i("div",{staticClass:"exclusive-button-container"},[t.shouldShowButton?i("button",{staticClass:"exclusive-button",class:{"button-disabled":t.isVipLocked},attrs:{disabled:t.isVipLocked},on:{click:function(e){return t.submitBuy(t.currentGradeInfo&&t.currentGradeInfo.amount,t.currentGradeInfo&&t.currentGradeInfo.grade,t.currentGradeInfo&&t.currentGradeInfo.name)}}},[t._v("\n        "+t._s(t.getButtonText())+"\n      ")]):t._e()]),t._v(" "),i("div",{staticClass:"benefits-container"},[i("div",{staticClass:"benefits-title"},[t._v(t._s(t.$t("vip.benefitsTitle")))]),t._v(" "),t.currentGradeInfo?i("div",{staticClass:"privilege-content"},[t.currentGradeInfo.privilege_description?i("div",{domProps:{innerHTML:t._s(t.getFilteredPrivilegeDescription())}}):i("div",{staticClass:"default-privilege"},[i("div",{staticClass:"benefit-item"},[i("div",{staticClass:"benefit-number"},[t._v("1.")]),t._v(" "),i("div",{staticClass:"benefit-content"},[i("div",{staticClass:"benefit-title"},[i("span",{staticClass:"benefit-icon"},[t._v("🎯")]),t._v("\n                "+t._s(t.$t("vip.exclusivePrivilege",{name:t.currentGradeInfo.name}))+"\n              ")]),t._v(" "),i("div",{staticClass:"benefit-description"},[i("strong",[t._v(t._s(t.$t("vip.upgradeCost"))+"：")]),t._v(t._s(t.$Currency.formatAmount(t.currentGradeInfo.amount))),i("br"),t._v(" "),i("strong",[t._v(t._s(t.$t("vip.memberLevel"))+"：")]),t._v(t._s(t.$t("vip.levelAuth",{level:t.currentGradeInfo.grade}))),i("br"),t._v("\n                "+t._s(t.$t("vip.upgradeNow"))+"\n              ")])])]),t._v(" "),void 0!==t.currentGradeInfo.number?i("div",{staticClass:"benefit-item"},[i("div",{staticClass:"benefit-number"},[t._v("2.")]),t._v(" "),i("div",{staticClass:"benefit-content"},[i("div",{staticClass:"benefit-title"},[i("span",{staticClass:"benefit-icon"},[t._v("📋")]),t._v("\n                "+t._s(t.$t("vip.benefits.dailyTasks"))+"\n              ")]),t._v(" "),i("div",{staticClass:"benefit-description"},[i("span",{domProps:{innerHTML:t._s(t.$t("vip.benefits.dailyTasksDesc",{number:t.currentGradeInfo.number}))}}),i("br"),t._v("\n                "+t._s(t.$t("vip.benefits.taskRewards"))),i("br"),t._v("\n                "+t._s(t.$t("vip.benefits.earnMore"))+"\n              ")])])]):t._e(),t._v(" "),void 0!==t.currentGradeInfo.daily_turntable_times?i("div",{staticClass:"benefit-item"},[i("div",{staticClass:"benefit-number"},[t._v(t._s(t.getLotteryBenefitNumber())+".")]),t._v(" "),i("div",{staticClass:"benefit-content"},[i("div",{staticClass:"benefit-title"},[i("span",{staticClass:"benefit-icon"},[t._v("🎰")]),t._v("\n                "+t._s(t.$t("vip.benefits.lotteryTimes"))+"\n              ")]),t._v(" "),i("div",{staticClass:"benefit-description"},[i("span",{domProps:{innerHTML:t._s(t.$t("vip.benefits.lotteryTimesDesc",{times:t.currentGradeInfo.daily_turntable_times}))}}),i("br"),t._v("\n                "+t._s(t.$t("vip.benefits.lotteryChance"))),i("br"),t._v("\n                "+t._s(t.$t("vip.benefits.lotteryRewards"))+"\n              ")])])]):t._e(),t._v(" "),void 0!==t.currentGradeInfo.commission?i("div",{staticClass:"benefit-item"},[i("div",{staticClass:"benefit-number"},[t._v(t._s(t.getCommissionBenefitNumber())+".")]),t._v(" "),i("div",{staticClass:"benefit-content"},[i("div",{staticClass:"benefit-title"},[i("span",{staticClass:"benefit-icon"},[t._v("💰")]),t._v("\n                "+t._s(t.$t("vip.referralReward"))+"\n              ")]),t._v(" "),i("div",{staticClass:"benefit-description"},[t._v("\n                "+t._s(t.$t("vip.referralDesc"))),i("br"),t._v(" "),i("strong",[t._v(t._s(t.$t("vip.referralPercent",{percent:t.currentGradeInfo.commission})))]),i("br"),t._v("\n                "+t._s(t.$t("vip.inviteMore"))+"\n              ")])])]):t._e()])]):t._e(),t._v(" "),t.currentGradeInfo?t._e():i("div",{staticClass:"benefit-item"},[i("div",{staticClass:"benefit-number"},[t._v("1.")]),t._v(" "),i("div",{staticClass:"benefit-content"},[i("div",{staticClass:"benefit-title"},[t._v(t._s(t.$t("vip.loading")))]),t._v(" "),i("div",{staticClass:"benefit-description"},[t._v("\n            "+t._s(t.$t("vip.loadingDesc"))+"\n          ")])])])])]),t._v(" "),i("Footer")],1)])])},staticRenderFns:[]};var r=i("VU/8")(s,n,!1,function(t){i("QHWA")},"data-v-5fcb563b",null);e.default=r.exports}});