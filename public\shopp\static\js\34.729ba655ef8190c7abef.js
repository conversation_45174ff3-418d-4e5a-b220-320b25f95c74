webpackJsonp([34],{MRpt:function(t,e){},tcoL:function(t,e,s){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i={name:"Register",components:{},props:["recommendId"],data:function(){return{postData:{dest:"62",username:"",password:"",re_password:"",smscode:"",code:"",code_rand:"",recommend:this.recommendId||""},recommendDis:!!this.recommendId,areaList:areaList,isSendCode:!1,isSubmit:!1,codeImg:"",showBrowserTips:!1,downUrl:"",isPlus:!1,isAgree:!0}},computed:{},watch:{},created:function(){var t=this,e=localStorage.Language||Language;this.downUrl=this.InitData.setting.app_down+e.toUpperCase(),this.$Model.SmsCode(function(e){t.areaList=e;var s=e.find(function(t){return"62"==t.id});t.postData.dest=s?"62":e[0].id}),this.getCode(),this.checkUserAgent()},mounted:function(){window.plus&&(this.isPlus=!0)},activated:function(){},destroyed:function(){},methods:{appDown:function(){var t=localStorage.Language||Language,e=this.InitData.setting.app_down+t.toUpperCase();this.$Util.OpenUrl(e)},openArticle:function(){this.$router.push("/article/terms/"+(this.InitData.disclaimerList.length?this.InitData.disclaimerList[0].id:""))},getCode:function(){this.postData.code_rand=(new Date).getTime(),this.codeImg=this.ApiUrl+"/api/Account/code?code_rand="+this.postData.code_rand},onSubmit:function(){var t=this;this.postData.username?this.postData.smscode||1!=this.InitData.setting.is_sms?this.postData.code||2!=this.InitData.setting.is_sms?this.postData.password?this.postData.re_password?this.postData.password==this.postData.re_password?this.isAgree?(this.isSubmit=!0,this.$Model.UserRegister(this.postData,function(e){t.isSubmit=!1,t.getCode()})):this.$Dialog.Toast(this.$t("register.placeholder[8]")):this.$Dialog.Toast(this.$t("register.placeholder[5]")):this.$Dialog.Toast(this.$t("register.placeholder[3]")):this.$Dialog.Toast(this.$t("register.placeholder[2]")):this.$Dialog.Toast(this.$t("register.placeholder[6]")):this.$Dialog.Toast(this.$t("register.placeholder[1]")):this.$Dialog.Toast(this.$t("register.placeholder[0]"))},getSmsCode:function(){var t=this;this.postData.username?this.postData.code?(this.isSendCode=!0,this.$Model.GetSMSCode({phone:this.postData.username,dest:this.postData.dest,code:this.postData.code,recommend:this.postData.recommend,code_rand:this.postData.code_rand},function(e){t.isSendCode=!1})):this.$Dialog.Toast(this.$t("register.placeholder[6]")):this.$Dialog.Toast(this.$t("register.placeholder[0]"))},checkUserAgent:function(t){var e=navigator.userAgent;/(MicroMessenger)/.test(e)&&(this.showBrowserTips=!0),/(iPod|iPhone|iPad)/.test(e)?t&&t("IOS"):t&&t("Android")}}},o={render:function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"Site PageBox"},[s("van-nav-bar",{attrs:{fixed:"",border:!1,"left-arrow":""},on:{"click-left":function(e){return t.$router.go(-1)},"click-right":function(e){t.$langList&&t.$router.push("/language")}},scopedSlots:t._u([t.$langList?{key:"right",fn:function(){return[s("svg",{staticStyle:{color:"#333",width:"18px",height:"18px"},attrs:{viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg"}},[s("path",{attrs:{d:"M511.777943 68.191078c-245.204631 0-443.586864 198.60429-443.586864 443.808922s198.382233 443.808922 443.586864 443.808922 444.030979-198.60429 444.030979-443.808922S756.982574 68.191078 511.777943 68.191078zM819.11568 334.476841 688.191838 334.476841c-14.423501-55.476499-34.617425-108.733447-61.245899-157.995407C708.606797 204.440206 776.509303 261.025968 819.11568 334.476841zM512 158.506037c37.058011 53.256948 65.906036 112.505353 84.767616 175.96978L427.232384 334.475817C446.093964 271.01139 474.941989 211.762985 512 158.506037zM168.491459 600.76158c-7.322779-28.40391-11.538801-58.139142-11.538801-88.76158s4.216022-60.35767 11.538801-88.76158l149.785421 0c-3.550873 29.069059-5.991458 58.582233-5.991458 88.76158s2.440585 59.69252 6.213515 88.76158L168.491459 600.76158zM204.662263 689.523159l130.923842 0c14.423501 55.476499 34.617425 108.733447 61.245899 158.217465C315.171146 819.780829 247.267617 762.974032 204.662263 689.523159zM335.586105 334.476841 204.662263 334.476841c42.605354-73.449849 110.508883-130.257669 192.168718-158.217465C370.202507 225.743394 350.009605 279.000342 335.586105 334.476841zM512 865.493963c-36.835953-53.256948-65.683978-112.505353-84.767616-175.96978l169.535231 0C577.682955 752.987586 548.835953 812.235992 512 865.493963zM615.851253 600.76158 408.148747 600.76158c-4.216022-29.069059-7.100722-58.582233-7.100722-88.76158s2.8847-59.69252 7.100722-88.76158l207.702506 0c4.216022 29.069059 7.100722 58.582233 7.100722 88.76158S620.067274 571.69252 615.851253 600.76158zM627.167996 847.51959c26.628474-49.485041 46.821375-102.519931 61.245899-157.995407l130.923842 0C776.510326 762.974032 708.606797 819.558771 627.167996 847.51959zM705.500039 600.76158c3.550873-29.069059 6.213515-58.582233 6.213515-88.76158s-2.440585-59.69252-6.213515-88.76158l149.785421 0c7.322779 28.40391 11.760858 58.139142 11.760858 88.76158s-4.216022 60.35767-11.760858 88.76158L705.500039 600.76158z",fill:"currentColor"}})])]},proxy:!0}:null],null,!0)}),t._v(" "),s("div",{staticClass:"Login ScrollBox"},[s("div",{staticClass:"register-title"},[t._v(t._s(t.InitData.setting&&t.InitData.setting.web_title||""))]),t._v(" "),s("div",{staticClass:"register-text"},[t._v(t._s(t.$t("register.text[0]")))]),t._v(" "),s("van-form",{on:{submit:t.onSubmit}},[s("div",{staticClass:"loginInputBox"},[s("van-field",{attrs:{"left-icon":"manager",clearable:"",border:!1},scopedSlots:t._u([{key:"left-icon",fn:function(){return[s("van-image",{staticStyle:{"margin-right":"8px","margin-top":"5px"},attrs:{width:"24",height:"24",src:"./static/icon/loginUserIcon.png"}})]},proxy:!0},{key:"input",fn:function(){return[s("van-dropdown-menu",[s("van-dropdown-item",{ref:"DropdownItem",attrs:{title:"+"+t.postData.dest}},[[s("ul",t._l(t.areaList,function(e,i){return s("li",{key:i,class:{on:t.postData.dest==e.id},on:{click:function(s){t.postData.dest=e.id,t.$refs.DropdownItem.toggle()}}},[t._v("\n                      +"+t._s(e.id)+"　"+t._s(e.name)+"\n                    ")])}),0)]],2)],1),t._v(" "),s("input",{directives:[{name:"model",rawName:"v-model.trim",value:t.postData.username,expression:"postData.username",modifiers:{trim:!0}}],staticStyle:{border:"0",flex:"1",width:"100px","padding-left":"50px",background:"transparent"},attrs:{type:"tel",placeholder:t.$t("register.placeholder[0]")},domProps:{value:t.postData.username},on:{input:function(e){e.target.composing||t.$set(t.postData,"username",e.target.value.trim())},blur:function(e){return t.$forceUpdate()}}})]},proxy:!0}])})],1),t._v(" "),s("div",{staticClass:"loginInputBox"},[s("van-field",{attrs:{"left-icon":"coupon",autocomplete:"off",type:"digit",placeholder:t.$t("register.placeholder[6]"),clearable:"",border:!1},scopedSlots:t._u([{key:"left-icon",fn:function(){return[s("van-image",{staticStyle:{"margin-right":"8px","margin-top":"10px"},attrs:{width:"28",height:"28",src:"./static/icon/codeIcon.png"}})]},proxy:!0},{key:"button",fn:function(){return[s("van-image",{staticStyle:{display:"block",cursor:"pointer"},attrs:{width:"100",height:"34",src:t.codeImg},on:{click:t.getCode}})]},proxy:!0}]),model:{value:t.postData.code,callback:function(e){t.$set(t.postData,"code","string"==typeof e?e.trim():e)},expression:"postData.code"}})],1),t._v(" "),t.InitData.setting&&1==t.InitData.setting.is_sms?s("div",{staticClass:"loginInputBox"},[s("van-field",{attrs:{"left-icon":"coupon",autocomplete:"off",type:"digit",placeholder:t.$t("register.placeholder[1]"),clearable:"",border:!1},scopedSlots:t._u([{key:"left-icon",fn:function(){return[s("van-image",{staticStyle:{"margin-right":"8px","margin-top":"10px"},attrs:{width:"28",height:"28",src:"./static/icon/codeIcon.png"}})]},proxy:!0},{key:"button",fn:function(){return[s("van-button",{attrs:{size:"mini",type:"info",round:"","native-type":"button",loading:t.isSendCode,"loading-text":t.$t("register.text[1]")},on:{click:t.getSmsCode}},[t._v(t._s(t.$t("register.text[2]")))])]},proxy:!0}],null,!1,2885150952),model:{value:t.postData.smscode,callback:function(e){t.$set(t.postData,"smscode","string"==typeof e?e.trim():e)},expression:"postData.smscode"}})],1):t._e(),t._v(" "),s("div",{staticClass:"loginInputBox"},[s("van-field",{attrs:{type:"password",placeholder:t.$t("register.placeholder[2]"),clearable:"",autocomplete:"off",border:!1},scopedSlots:t._u([{key:"left-icon",fn:function(){return[s("van-image",{staticStyle:{"margin-right":"8px","margin-top":"5px"},attrs:{width:"24",height:"24",src:"./static/icon/loginPaswordIcon.png"}})]},proxy:!0}]),model:{value:t.postData.password,callback:function(e){t.$set(t.postData,"password","string"==typeof e?e.trim():e)},expression:"postData.password"}})],1),t._v(" "),s("div",{staticClass:"loginInputBox"},[s("van-field",{attrs:{type:"password",placeholder:t.$t("register.placeholder[3]"),clearable:"",autocomplete:"off",border:!1},scopedSlots:t._u([{key:"left-icon",fn:function(){return[s("van-image",{staticStyle:{"margin-right":"8px","margin-top":"5px"},attrs:{width:"24",height:"24",src:"./static/icon/loginPaswordIcon.png"}})]},proxy:!0}]),model:{value:t.postData.re_password,callback:function(e){t.$set(t.postData,"re_password","string"==typeof e?e.trim():e)},expression:"postData.re_password"}})],1),t._v(" "),s("div",{staticClass:"loginInputBox"},[s("van-field",{attrs:{"left-icon":"invition",readonly:t.recommendDis,placeholder:t.$t("register.placeholder[4]"),clearable:"",autocomplete:"off",border:!1},scopedSlots:t._u([{key:"left-icon",fn:function(){return[s("van-image",{staticStyle:{"margin-right":"8px","margin-top":"5px"},attrs:{width:"24",height:"24",src:"./static/icon/invitationCode.png"}})]},proxy:!0}]),model:{value:t.postData.recommend,callback:function(e){t.$set(t.postData,"recommend","string"==typeof e?e.trim():e)},expression:"postData.recommend"}})],1),t._v(" "),s("van-cell",{attrs:{border:!1}},[s("van-checkbox",{staticStyle:{"margin-left":"22px"},attrs:{slot:"icon","checked-color":"#FF0F23","icon-size":"16"},slot:"icon",model:{value:t.isAgree,callback:function(e){t.isAgree=e},expression:"isAgree"}}),t._v(" "),s("a",{staticStyle:{"margin-left":"8px","font-size":"12px",color:"#999","text-decoration":"underline"},attrs:{href:"javascript:;"},on:{click:t.openArticle}},[t._v(t._s(t.$t("register.text[6]")))])],1),t._v(" "),s("div",{staticStyle:{padding:"20px 16px"}},[s("van-button",{staticClass:"loginBtn",staticStyle:{"font-size":"18px"},attrs:{size:"large",block:"",type:"danger",round:"",loading:t.isSubmit,"loading-text":t.$t("register.text[3]")}},[t._v(t._s(t.$t("register.text[4]"))+"\n        ")]),t._v(" "),t.isPlus?t._e():s("div",{staticStyle:{"font-size":"12px","margin-top":"10px",color:"#FF0F23"},attrs:{slot:"a"},on:{click:t.appDown},slot:"a"},[t._v("\n          "+t._s(t.$t("register.text[5]"))+"\n        ")]),t._v(" "),t.isPlus?s("i18n",{staticStyle:{"text-align":"left",color:"#fff","margin-top":"30px"},attrs:{path:"register.i18n[0]",tag:"div"}},[s("router-link",{staticClass:"href",attrs:{slot:"a",to:"/login"},slot:"a"},[t._v(t._s(t.$t("register.i18n[1]")))]),t._v(" "),s("router-link",{staticClass:"href fr",attrs:{slot:"line",to:"/line"},slot:"line"},[t._v(t._s(t.$t("line")))])],1):t._e()],1)],1)],1),t._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:t.showBrowserTips,expression:"showBrowserTips"}],staticClass:"BrowserTips"},[s("img",{attrs:{src:"./static/images/down-zy.png",width:"80%"}})])],1)},staticRenderFns:[]};var a=s("VU/8")(i,o,!1,function(t){s("MRpt")},"data-v-2eae9e74",null);e.default=a.exports}});