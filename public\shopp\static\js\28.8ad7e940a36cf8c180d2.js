webpackJsonp([28],{"h/+L":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"Site PageBox"},[n("van-nav-bar",{attrs:{fixed:"",border:!1,title:t.$t("common[0]"),"left-arrow":""},on:{"click-left":function(e){return t.$router.go(-1)}}}),t._v(" "),n("iframe",{attrs:{src:t.InitData.setting.service_url,width:"100%",height:"100%",frameborder:"0"}})],1)},staticRenderFns:[]};var a=n("VU/8")({name:"Service",components:{},props:{},data:function(){return{}},computed:{},watch:{},created:function(){},mounted:function(){},activated:function(){},destroyed:function(){},methods:{}},r,!1,function(t){n("wfG1")},"data-v-40a924ca",null);e.default=a.exports},wfG1:function(t,e){}});