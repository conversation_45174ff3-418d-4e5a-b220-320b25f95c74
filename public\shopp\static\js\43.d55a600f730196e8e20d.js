webpackJsonp([43],{I37D:function(t,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var s={name:"Wallet",components:{},props:["walletType"],data:function(){return{listData:"",isLoad:!1,isFinished:!1,isRefresh:!1,pageNo:1,tabsState:1,tabsIndex:0,taskTabs:[{type:1,text:this.$t("wallet.default[3]")},{type:3,text:this.$t("wallet.default[4]")}],showTurn:!1,showDraw:!1,showPicker:!1,selectBank:"",isSubmit:!1,turnData:{username:"",turn_money:"",drawword:""},drawData:{draw_type:"bank",bank:"",user_bank_id:"",draw_money:100,drawword:""}}},computed:{cardList:function(){return this.$store.state.BankCardList.flatMap(function(t){return t.bank_name+" "+t.card_no.replace(/^(.{4}).*(.{4})$/,"$1 **** $2")})},currentUserBalance:function(){return parseFloat(this.UserInfo.balance)||0},feeRate:function(){return this.InitData.setting&&1==this.InitData.setting.withdrawal_fee_switch&&parseFloat(this.InitData.setting.withdrawal_bank_fee_rate)||0},withdrawalFee:function(){var t=parseFloat(this.drawData.draw_money)||0;return this.feeRate>0?t*(this.feeRate/100):0},totalDeduction:function(){return(parseFloat(this.drawData.draw_money)||0)+this.withdrawalFee}},watch:{$route:function(){var t=this;this.walletType?(this.tabsIndex=this.taskTabs.findIndex(function(a){return a.type==t.walletType}),this.tabsState=this.walletType):(this.tabsState=1,this.tabsIndex=0),this.getListData("init")}},created:function(){var t=this;this.$Model.GetBankCardList(),this.$parent.navBarTitle=this.$t("wallet.default[0]"),this.listData=this.taskTabs.flatMap(function(t){return[""]}),this.walletType&&(this.tabsIndex=this.taskTabs.findIndex(function(a){return a.type==t.walletType}),this.tabsState=this.walletType),this.getListData("init"),this.drawData.draw_money=Number(this.InitData.setting.min_w||100)},mounted:function(){},activated:function(){},destroyed:function(){},methods:{onWithdrawAmountChange:function(t){},goTurn:function(t){var a=this;1!=this.UserInfo.is_fund_password?this.$Dialog.Alert(this.$t("wallet.msg[0]"),function(){a.$router.push("/user/info")}):"turn"==t?this.showTurn=!0:this.cardList.length?this.showDraw=!0:this.$Dialog.Alert(this.$t("wallet.msg[1]"),function(){a.$router.push("/user/bankCard")})},openInfo:function(t){0==this.tabsIndex&&this.$router.push("/user/recharge/"+t)},onConfirm:function(t,a){this.drawData.user_bank_id=this.BankCardList[a].id,this.drawData.bank=this.BankCardList[a].bank_name,this.drawData.ifsc=this.BankCardList[a].bank_branch_name,this.selectBank=t,this.showPicker=!1},submitDraw:function(){var t=this;if(this.drawData.user_bank_id)if(this.drawData.draw_money){var a=parseFloat(this.drawData.draw_money)||0,e=this.currentUserBalance,s=this.withdrawalFee,i=this.totalDeduction;if(i>e){var n=i-e,r="";return r=s>0?this.$t("wallet.warning.insufficientBalanceWithFee",{withdrawAmount:a.toFixed(2),fee:s.toFixed(2),totalRequired:i.toFixed(2),currentBalance:e.toFixed(2),shortfall:n.toFixed(2),currency:this.InitData.currency||"USDT"}):this.$t("wallet.warning.insufficientBalance",{withdrawAmount:a.toFixed(2),currentBalance:e.toFixed(2),shortfall:n.toFixed(2),currency:this.InitData.currency||"USDT"}),void this.$Dialog.Toast(r)}this.drawData.drawword?(this.isSubmit=!0,this.$Model.Draw(this.drawData,function(a){t.isSubmit=!1,1==a.code&&(t.showDraw=!1,t.drawData={draw_type:"bank",bank:"",user_bank_id:"",draw_money:Number(t.InitData.setting.min_w||100),drawword:""},t.selectBank="",t.getListData("init"))})):this.$Dialog.Toast(this.$t("wallet.placeholder[2]"))}else this.$Dialog.Toast(this.$t("wallet.placeholder[1]"));else this.$Dialog.Toast(this.$t("wallet.placeholder[3]"))},onLoad:function(){this.getListData("load")},changeTabs:function(t){this.tabsState=this.taskTabs[t].type,this.getListData("init")},getListData:function(t){var a=this;this.isLoad=!0,this.isRefresh=!1,"load"==t?this.pageNo+=1:(this.pageNo=1,this.isFinished=!1),0==this.tabsIndex?this.$Model.GetRechargeRecord({state:0,page_no:this.pageNo},function(e){a.isLoad=!1,1==e.code?(a.listData[a.tabsIndex]="load"==t?a.listData[a.tabsIndex].concat(e.info):e.info,a.pageNo==e.data_total_page?a.isFinished=!0:a.isFinished=!1):(a.listData[a.tabsIndex]="",a.isFinished=!0)}):1==this.tabsIndex?this.$Model.GetDrawRecord({state:0,page_no:this.pageNo},function(e){a.isLoad=!1,1==e.code?(a.listData[a.tabsIndex]="load"==t?a.listData[a.tabsIndex].concat(e.info):e.info,a.pageNo==e.data_total_page?a.isFinished=!0:a.isFinished=!1):(a.listData[a.tabsIndex]="",a.isFinished=!0)}):this.$Model.FundDetails({trade_type:11,page_no:this.pageNo},function(e){a.isLoad=!1,1==e.code?(a.listData[a.tabsIndex]="load"==t?a.listData[a.tabsIndex].concat(e.list):e.list,a.pageNo==e.data_total_page?a.isFinished=!0:a.isFinished=!1):(a.listData[a.tabsIndex]="",a.isFinished=!0)})},onRefresh:function(){this.getListData("init")}}},i={render:function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"PageBox"},[e("div",{staticClass:"ScrollBox Wallet"},[e("div",{staticClass:"balance"},[t._v(t._s(t.UserInfo.balance))]),t._v(" "),e("div",{staticClass:"tool"},[e("div",[e("router-link",{attrs:{to:"/user/watchPay"}},[t._v(t._s(t.$t("wallet.default[1]")))]),t._v(" "),e("a",{attrs:{href:"javascript:;"},on:{click:function(a){return t.goTurn("draw")}}},[t._v(t._s(t.$t("wallet.default[2]")))])],1)]),t._v(" "),e("van-tabs",{attrs:{type:"card",border:!1,color:"#292929","title-inactive-color":"#292929"},on:{change:t.changeTabs},model:{value:t.tabsIndex,callback:function(a){t.tabsIndex=a},expression:"tabsIndex"}},t._l(t.taskTabs,function(a,s){return e("van-tab",{key:s,staticStyle:{padding:"0 12px"},attrs:{title:a.text}},[e("van-pull-refresh",{on:{refresh:t.onRefresh},model:{value:t.isRefresh,callback:function(a){t.isRefresh=a},expression:"isRefresh"}},[e("van-list",{class:{Empty:!t.listData[t.tabsIndex].length},attrs:{finished:t.isFinished,"finished-text":t.listData[t.tabsIndex].length?t.$t("vanPull[0]"):t.$t("vanPull[1]")},on:{load:t.onLoad},model:{value:t.isLoad,callback:function(a){t.isLoad=a},expression:"isLoad"}},[0==t.tabsIndex||1==t.tabsIndex?e("div",t._l(t.listData[t.tabsIndex],function(a,s){return e("van-cell",{key:a.dan,staticClass:"FundItem",attrs:{border:!1},scopedSlots:t._u([{key:"title",fn:function(){return[e("div",[e("span",[t._v(t._s(a.dan))]),t._v(" "),e("span",[t._v(t._s(a.adddate))])]),t._v(" "),e("div",[e("span",{staticClass:"money"},[t._v("\n                      "+t._s(Number(a.money))+"\n                      "),a.fee||a.draw_fee||a.service_fee?e("span",{staticStyle:{color:"#999","font-size":"0.9em"}},[t._v("\n                        ("+t._s(Number(a.fee||a.draw_fee||a.service_fee||0))+")\n                      ")]):t._e()]),t._v(" "),e("span",[t._v(t._s(a.status_desc))])])]},proxy:!0}],null,!0)},[e("div",{class:"icon tag"+t.tabsIndex,attrs:{slot:"icon"},slot:"icon"},[t._v("\n                  "+t._s(0==t.tabsIndex?t.$t("wallet.default[5]"):t.$t("wallet.default[6]"))+"\n                ")])])}),1):e("div",t._l(t.listData[t.tabsIndex],function(a,s){return e("van-cell",{key:a.order_id,staticClass:"FundItem",attrs:{border:!1},scopedSlots:t._u([{key:"title",fn:function(){return[e("div",[e("span",[t._v(t._s(a.trade_number))]),t._v(" "),e("span",[t._v(t._s(a.trade_time))])]),t._v(" "),e("div",[e("span",{staticClass:"money"},[t._v(t._s(a.jj)+t._s(a.trade_amount))]),t._v(" "),e("span",[t._v(t._s(a.remarks))])])]},proxy:!0}],null,!0)},[e("div",{class:"icon tag"+t.tabsIndex,attrs:{slot:"icon"},slot:"icon"},[t._v("\n                  转\n                ")])])}),1)])],1)],1)}),1)],1),t._v(" "),e("van-action-sheet",{staticClass:"DrawPopup",attrs:{title:t.$t("wallet.default[2]"),"close-on-popstate":""},model:{value:t.showDraw,callback:function(a){t.showDraw=a},expression:"showDraw"}},[e("div",{staticClass:"DrawPopupBox"},[e("van-field",{staticStyle:{height:"0",width:"0",padding:"0",position:"absolute"},attrs:{type:"password",autocomplete:"off"}}),t._v(" "),e("van-field",{attrs:{type:"text",label:t.$t("wallet.label[0]"),placeholder:t.$t("wallet.placeholder[0]"),size:"large",readonly:""},on:{click:function(a){t.showPicker=!0}},model:{value:t.selectBank,callback:function(a){t.selectBank=a},expression:"selectBank"}}),t._v(" "),e("van-field",{attrs:{type:"number",label:t.$t("wallet.label[1]"),placeholder:t.$t("wallet.placeholder[1]"),clearable:"",size:"large"},on:{input:t.onWithdrawAmountChange},model:{value:t.drawData.draw_money,callback:function(a){t.$set(t.drawData,"draw_money","string"==typeof a?a.trim():a)},expression:"drawData.draw_money"}}),t._v(" "),t.drawData.draw_money&&parseFloat(t.drawData.draw_money)>0?e("div",{staticClass:"withdraw-info"},[e("div",{staticClass:"info-row"},[e("span",{staticClass:"info-label"},[t._v(t._s(t.$t("wallet.info.currentBalance"))+":")]),t._v(" "),e("span",{staticClass:"info-value balance"},[t._v(t._s(t.InitData.currency)+t._s(t.currentUserBalance.toFixed(2)))])]),t._v(" "),e("div",{staticClass:"info-row"},[e("span",{staticClass:"info-label"},[t._v(t._s(t.$t("wallet.info.withdrawAmount"))+":")]),t._v(" "),e("span",{staticClass:"info-value"},[t._v(t._s(t.InitData.currency)+t._s(parseFloat(t.drawData.draw_money).toFixed(2)))])]),t._v(" "),t.withdrawalFee>0?e("div",{staticClass:"info-row"},[e("span",{staticClass:"info-label"},[t._v(t._s(t.$t("wallet.info.fee"))+" ("+t._s(t.feeRate)+"%):")]),t._v(" "),e("span",{staticClass:"info-value fee"},[t._v(t._s(t.InitData.currency)+t._s(t.withdrawalFee.toFixed(2)))])]):t._e(),t._v(" "),e("div",{staticClass:"info-row total-row"},[e("span",{staticClass:"info-label"},[t._v(t._s(t.$t("wallet.info.totalDeduction"))+":")]),t._v(" "),e("span",{staticClass:"info-value total"},[t._v(t._s(t.InitData.currency)+t._s(t.totalDeduction.toFixed(2)))])]),t._v(" "),t.totalDeduction>t.currentUserBalance?e("div",{staticClass:"info-row warning-row"},[e("span",{staticClass:"warning-text"},[t._v("\n            ⚠️ "+t._s(t.$t("wallet.warning.insufficientFunds"))+"\n            ("+t._s(t.$t("wallet.warning.shortfall"))+": "+t._s(t.InitData.currency)+t._s((t.totalDeduction-t.currentUserBalance).toFixed(2))+")\n          ")])]):t._e()]):t._e(),t._v(" "),e("van-field",{attrs:{type:"password",label:t.$t("wallet.label[2]"),placeholder:t.$t("wallet.placeholder[2]"),clearable:"",size:"large"},model:{value:t.drawData.drawword,callback:function(a){t.$set(t.drawData,"drawword","string"==typeof a?a.trim():a)},expression:"drawData.drawword"}})],1),t._v(" "),e("div",{staticStyle:{padding:"0 16px",margin:"16px 0"}},[t.InitData.setting&&1==t.InitData.setting.withdrawal_fee_switch?e("div",{staticStyle:{color:"#999","font-size":"14px","text-align":"center","margin-bottom":"12px"}},[t._v("\n        "+t._s(t.$t("wallet.label[8]"))+t._s(Number(t.InitData.setting.withdrawal_bank_fee_rate||0))+"%\n      ")]):t._e(),t._v(" "),e("van-button",{staticClass:"loginBtn",staticStyle:{"font-size":"16px"},attrs:{type:"danger",block:""},on:{click:t.submitDraw}},[t._v(t._s(t.$t("wallet.label[3]")))])],1),t._v(" "),t.InitData.drawNotice&&t.InitData.drawNotice.length?e("van-divider",{staticStyle:{"line-height":"1.2"}},[e("div",{staticClass:"DrawNoticeTip",domProps:{innerHTML:t._s(t.InitData.drawNotice[0].title)}})]):t._e(),t._v(" "),t.InitData.drawNotice&&t.InitData.drawNotice.length?e("div",{staticStyle:{color:"grey",padding:"10px 16px"},domProps:{innerHTML:t._s(t.InitData.drawNotice[0].content)}}):t._e()],1),t._v(" "),e("van-popup",{staticClass:"PickerPopup",attrs:{position:"bottom"},model:{value:t.showPicker,callback:function(a){t.showPicker=a},expression:"showPicker"}},[e("van-picker",{attrs:{"show-toolbar":"",columns:t.cardList},on:{confirm:t.onConfirm,cancel:function(a){t.showPicker=!1}}})],1)],1)},staticRenderFns:[]};var n=e("VU/8")(s,i,!1,function(t){e("MDyy")},"data-v-00d16715",null);a.default=n.exports},MDyy:function(t,a){}});