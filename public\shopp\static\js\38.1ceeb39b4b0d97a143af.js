webpackJsonp([38],{QRlu:function(t,i,a){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var e={render:function(){var t=this,i=t.$createElement,a=t._self._c||i;return a("div",{staticClass:"PageBox"},[a("div",{staticClass:"ScrollBox"},[a("van-field",{staticClass:"mt15",attrs:{size:"large",label:t.$t("buyVip[1]"),placeholder:t.$t("buyVip[2]"),clearable:""},model:{value:t.postData.xusername,callback:function(i){t.$set(t.postData,"xusername",i)},expression:"postData.xusername"}}),t._v(" "),a("van-field",{attrs:{readonly:"",value:t.vipName,label:t.$t("buyVip[3]"),placeholder:"--"+t.$t("buyVip[4]")+"--"},on:{click:function(i){t.showPicker=!0}}}),t._v(" "),a("div",{staticStyle:{padding:"20px 16px"}},[a("van-button",{staticStyle:{"font-size":"16px"},attrs:{type:"danger",block:""},on:{click:t.submitBuy}},[t._v(t._s(t.$t("buyVip[6]")))])],1)],1),t._v(" "),a("van-popup",{attrs:{position:"bottom"},model:{value:t.showPicker,callback:function(i){t.showPicker=i},expression:"showPicker"}},[a("van-picker",{attrs:{"show-toolbar":"",columns:t.pickerList},on:{confirm:t.onConfirm,cancel:function(i){t.showPicker=!1}}})],1)],1)},staticRenderFns:[]};var s=a("VU/8")({name:"BuyVip",components:{},props:[],data:function(){return{postData:{xusername:"",grade:""},showPicker:!1,pickerList:"",vipList:"",vipName:""}},computed:{},watch:{},created:function(){this.$parent.navBarTitle=this.$t("buyVip[0]"),this.vipList=this.InitData.UserGradeList.filter(function(t){return 1!=t.grade}),this.pickerList=this.vipList.flatMap(function(t){return t.name})},mounted:function(){},activated:function(){},destroyed:function(){},methods:{onConfirm:function(t,i){this.vipName=t,this.postData.grade=this.vipList[i].grade,this.showPicker=!1},submitBuy:function(){var t=this;this.postData.xusername?this.postData.grade?this.$Dialog.Confirm(this.$t("buyVip[5]",{user:this.postData.xusername,grade:this.vipName}),function(){t.$Model.BuyVip(t.postData)}):this.$Dialog.Toast(this.$t("buyVip[4]")):this.$Dialog.Toast(this.$t("buyVip[2]"))}}},e,!1,function(t){a("g/4z")},"data-v-1d48a2a2",null);i.default=s.exports},"g/4z":function(t,i){}});