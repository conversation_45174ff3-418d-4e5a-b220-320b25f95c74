webpackJsonp([27],{hqKt:function(t,s){},pM6n:function(t,s,a){"use strict";Object.defineProperty(s,"__esModule",{value:!0});var i={render:function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("div",{staticClass:"PageBox"},[a("div",{staticClass:"ScrollBox"},[a("van-tabs",{attrs:{ellipsis:!1,border:!1,color:"#4087f1","title-active-color":"#4087f1",background:"#151d31","title-inactive-color":"#bbb","line-width":"60"},on:{change:t.changeTabs},model:{value:t.tabsIndex,callback:function(s){t.tabsIndex=s},expression:"tabsIndex"}},t._l(t.InitData.taskclasslist.filter(function(t){return 1==t.state}),function(s){return a("van-tab",{key:s.group_id,attrs:{title:s.group_name}},[a("van-pull-refresh",{on:{refresh:t.onRefresh},model:{value:t.isRefresh,callback:function(s){t.isRefresh=s},expression:"isRefresh"}},[a("van-list",{class:{Empty:!t.listData[t.tabsIndex].length},attrs:{finished:t.isFinished,"finished-text":t.listData[t.tabsIndex].length?t.$t("vanPull[0]"):t.$t("vanPull[1]")},on:{load:t.onLoad},model:{value:t.isLoad,callback:function(s){t.isLoad=s},expression:"isLoad"}},t._l(t.listData[t.tabsIndex],function(s){return a("div",{key:s.task_id,staticClass:"TaskItem"},[a("van-cell",{attrs:{border:!1,"title-class":"post"},scopedSlots:t._u([{key:"title",fn:function(){return[a("h4",[t._v(t._s(s.title))]),t._v(" "),a("p",[a("span",[t._v(t._s(t.$t("postRecord[2]"))+":"),a("em",[t._v(t._s(s.total_number))])]),t._v(" "),a("span",[t._v(t._s(t.$t("postRecord[3]"))+":"),a("em",[t._v(t._s(Number(s.total_number)-Number(s.surplus_number)))])])]),t._v(" "),a("p",[t._v(t._s(t.$t("postRecord[4]"))+":"+t._s(s.end_time))])]},proxy:!0}],null,!0)},[a("div",{staticClass:"icon",attrs:{slot:"icon"},slot:"icon"},[a("a",{attrs:{href:"javascript:;"},on:{click:function(a){return a.stopPropagation(),t.$Util.OpenUrl(s.link_info)}}},[a("img",{attrs:{src:t.InitData.setting.up_url+s.icon}})]),t._v(" "),a("van-tag",{attrs:{type:"primary"}},[t._v(t._s(s.vip_dec))])],1),t._v(" "),t._v(" "),a("p",{class:"state"+s.status},[t._v(t._s(s.status_dec))]),t._v("\n                "+t._s(t.$Currency.getSymbol())),a("b",[t._v(t._s(Number(s.reward_price)))])]),t._v(" "),2!=s.status&&5!=s.status?a("div",{staticClass:"button"},[3==s.status?a("van-button",{attrs:{type:"info",round:"",size:"mini"},on:{click:function(a){return t.$router.push("/user/auditRecord?taskId="+s.task_id)}}},[t._v(t._s(t.$t("postRecord[5]")))]):t._e(),t._v(" "),1==s.status?a("van-button",{attrs:{round:"",size:"mini"},on:{click:function(a){return t.cancelTask(s.task_id)}}},[t._v(t._s(t.$t("postRecord[6]")))]):t._e(),t._v(" "),1==s.status?a("van-button",{attrs:{type:"info",round:"",size:"mini"},on:{click:function(a){return t.$router.push("/user/postTask/"+s.task_id)}}},[t._v(t._s(t.$t("postRecord[7]")))]):t._e()],1):t._e()],1)}),0)],1)],1)}),1)],1)])},staticRenderFns:[]};var n=a("VU/8")({name:"PostRecord",components:{},props:["taskType"],data:function(){return{listData:"",isLoad:!1,isFinished:!1,isRefresh:!1,pageNo:1,taskGroupId:"",tabsIndex:0,isStore:!1}},computed:{},watch:{$route:function(){this.initData(this.InitData.taskclasslist.filter(function(t){return 1==t.state}))}},created:function(){this.$parent.navBarTitle=this.$t("postRecord[0]"),this.listData=this.InitData.taskclasslist.filter(function(t){return 1==t.state}).flatMap(function(t){return[""]}),this.InitData.taskclasslist.length&&this.initData(this.InitData.taskclasslist.filter(function(t){return 1==t.state}))},mounted:function(){},activated:function(){},destroyed:function(){},methods:{initData:function(t){var s=this;this.taskType?(this.tabsIndex=t.findIndex(function(t){return t.group_id==s.taskType}),this.taskGroupId=this.taskType):(this.tabsIndex=0,this.taskGroupId=t[0].group_id),this.getListData("init")},onLoad:function(){this.getListData("load")},changeTabs:function(t){this.taskGroupId=this.InitData.taskclasslist.filter(function(t){return 1==t.state})[t].group_id,this.$router.push("/user/postRecord/"+this.taskGroupId)},getListData:function(t){var s=this;this.isLoad=!0,this.isRefresh=!1,"load"==t?this.pageNo+=1:(this.pageNo=1,this.isFinished=!1),this.$Model.GetTaskList({group_id:this.taskGroupId,page_no:this.pageNo,is_u:1},function(a){s.isLoad=!1,1==a.code?(s.listData[s.tabsIndex]="load"==t?s.listData[s.tabsIndex].concat(a.info):a.info,s.pageNo==a.data_total_page?s.isFinished=!0:s.isFinished=!1):(s.listData[s.tabsIndex]="",s.isFinished=!0)})},onRefresh:function(){this.getListData("init")},auditTask:function(){},cancelTask:function(t){var s=this;this.$Model.CancelTask(t,function(t){1==t.code&&s.getListData("init")})}}},i,!1,function(t){a("hqKt")},"data-v-441f3462",null);s.default=n.exports}});