webpackJsonp([13],{"1iJ9":function(t,e){},GK62:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=a("oTBK"),s={name:"WatchPay",mixins:[a.n(n).a],data:function(){return{selectedPaymentMethod:null,paymentAmount:"",isSubmitting:!1,currentLanguage:"en",paymentMethods:[],isLoading:!0,selectedCountry:"ID",hasJumped:!1,selectedBankCode:"",bankList:[],showBankPicker:!1}},computed:{UserInfo:function(){return this.$store.state.UserInfo||{}},InitData:function(){return this.$store.state.InitData||{}},selectedPaymentMethodDetails:function(){var t=this;return this.selectedPaymentMethod&&this.paymentMethods.length?this.paymentMethods.find(function(e){return e.code+"_"+e.recharge_id===t.selectedPaymentMethod}):null},requiresBankCode:function(){return this.selectedPaymentMethodDetails&&"watch_pay"===this.selectedPaymentMethodDetails.channel_type},selectedBankName:function(){var t=this;if(!this.selectedBankCode||!this.bankList.length)return"Bank Central Asia";if("string"==typeof this.bankList[0])return this.selectedBankCode;var e=this.bankList.find(function(e){return e.bank_id==t.selectedBankCode});return e?e.bank:this.selectedBankCode}},created:function(){this.currentLanguage=localStorage.Language||"en",this.$route.query.amount&&(this.paymentAmount=this.$route.query.amount),this.$Model.GetUserInfo(),this.loadPaymentMethods(),this.initBankList()},beforeDestroy:function(){this.hasJumped=!1},methods:{goBack:function(){this.$router.go(-1)},loadPaymentMethods:function(){var t=this;this.isLoading=!0,this.$Model.GetPaymentTypes({country:this.selectedCountry},function(e){t.isLoading=!1,1===e.code&&e.data?(t.paymentMethods=e.data.map(function(t){return{code:t.code,name:t.name,recharge_id:t.recharge_id,mode:t.mode,type:t.type,channel_type:t.channel_type,min_amount:t.min_amount,max_amount:t.max_amount}}),t.paymentMethods.length>0&&t.paymentMethods[0].code&&(t.selectedPaymentMethod=t.paymentMethods[0].code+"_"+t.paymentMethods[0].recharge_id)):(t.$Dialog.Toast(e.code_dec||t.$t("vanPull[1]")),t.paymentMethods=[])})},selectPaymentMethod:function(t){this.selectedPaymentMethod=t,this.selectedBankCode=""},formatAmountRange:function(t){return(Number(t)||0).toLocaleString()},initBankList:function(){this.InitData.BanksList&&this.InitData.BanksList.length>0?this.bankList=this.InitData.BanksList.filter(function(t){return t.enabled&&"USDT"!==t.bank.toUpperCase()}).map(function(t){return t.bank}):this.bankList=["Bank Central Asia","Bank Mandiri","Bank Rakyat Indonesia","Bank Negara Indonesia","Bank CIMB Niaga","Bank Danamon","Bank Permata","Bank Maybank Indonesia"],this.bankList.length>0&&!this.selectedBankCode&&(this.selectedBankCode=this.bankList[0])},onBankConfirm:function(t,e){Array.isArray(this.bankList)&&this.bankList[e]&&(this.selectedBankCode=t),this.showBankPicker=!1},getAmountRangeText:function(){var t=this,e=this.paymentMethods.find(function(e){return e.code+"_"+e.recharge_id===t.selectedPaymentMethod});if(e){var a=Number(e.min_amount),n=Number(e.max_amount);return this.$t("globalPay.amountRange")+": "+this.formatAmountRange(a)+" ~ "+this.formatAmountRange(n)}return""},onAmountChange:function(){},formatDisplayAmount:function(t){return(Number(t)||0).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})},createPaymentOrder:function(){var t=this;if(!this.isSubmitting&&!this.hasJumped)if(this.selectedPaymentMethod)if(this.paymentAmount)if(!this.requiresBankCode||this.selectedBankCode){var e=parseFloat(this.paymentAmount),a=this.paymentMethods.find(function(e){return e.code+"_"+e.recharge_id===t.selectedPaymentMethod});if(a){var n=Number(a.min_amount),s=Number(a.max_amount);if(e<n)return void this.$Dialog.Toast(this.$t("recharge.placeholder[3]",{currency:"RP",min:n.toLocaleString()}));if(e>s)return void this.$Dialog.Toast(this.$t("recharge.placeholder[4]",{currency:"RP",max:s.toLocaleString()}))}this.isSubmitting=!0;var i=this.paymentMethods.find(function(e){return e.code+"_"+e.recharge_id===t.selectedPaymentMethod}),o=i?i.code:this.selectedPaymentMethod.split("_")[0],r={country_code:this.selectedCountry,pay_type:o,recharge_id:i?i.recharge_id:null,amount:this.paymentAmount,currency:"IDR"};i&&"watch_pay"===i.channel_type&&this.selectedBankCode&&(r.bank_code=this.selectedBankCode),this.$Model.CreateUnifiedOrder(r,function(e){if(t.isSubmitting=!1,console.log("CreateUnifiedOrder 返回数据:",e),1===e.code&&e.data)if(e.data.pay_url&&!t.hasJumped){console.log("跳转到支付URL:",e.data.pay_url),t.hasJumped=!0;try{window.location.href=e.data.pay_url}catch(t){console.error("window.location.href 跳转失败:",t),window.open(e.data.pay_url,"_blank")}}else e.data.order_no&&!t.hasJumped?(t.hasJumped=!0,t.$router.push({name:"watchPayOrder",params:{orderNumber:e.data.order_no}})):t.$Dialog.Toast(t.$t("globalPay.messages.orderCreated"));else t.$Dialog.Toast(e.code_dec||e.msg||t.$t("globalPay.messages.paymentFailed"))})}else this.$Dialog.Toast(this.$t("globalPay.selectBank"));else this.$Dialog.Toast(this.$t("recharge.placeholder[0]"));else this.$Dialog.Toast(this.$t("recharge.placeholder[1]"))}}},i={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"PageBox watch-pay-page"},[a("van-nav-bar",{staticClass:"watch-pay-navbar",attrs:{fixed:"",border:!1,title:t.$t("recharge.default[0]"),"left-arrow":""},on:{"click-left":t.goBack}}),t._v(" "),a("div",{staticClass:"ScrollBox WatchPay"},[a("div",{staticClass:"amount-display-card"},[a("div",{staticClass:"amount-label"},[t._v(t._s(t.$t("user.default[4]")))]),t._v(" "),a("div",{staticClass:"amount-value"},[t._v("RP"+t._s(t.UserInfo.balance||0))]),t._v(" "),a("div",{staticClass:"amount-subtitle"},[t._v(t._s(t.$t("globalPay.description")))])]),t._v(" "),a("div",{staticClass:"payment-section"},[a("div",{staticClass:"section-title"},[t._v(t._s(t.$t("globalPay.selectPaymentMethod")))]),t._v(" "),t.isLoading?a("div",{staticClass:"loading-container"},[a("van-loading",{attrs:{type:"spinner",color:"#FF0F23"}},[t._v(t._s(t.$t("recharge.default[8]")))])],1):t.paymentMethods.length>0?a("div",{staticClass:"payment-methods-scroll"},t._l(t.paymentMethods,function(e){return a("div",{key:e.code+"_"+e.recharge_id,staticClass:"payment-method-item",class:{active:t.selectedPaymentMethod===e.code+"_"+e.recharge_id},on:{click:function(a){return t.selectPaymentMethod(e.code+"_"+e.recharge_id)}}},[t.selectedPaymentMethod===e.code+"_"+e.recharge_id?a("div",{staticClass:"method-check"},[t._v("✓")]):t._e(),t._v(" "),a("div",{staticClass:"method-name"},[t._v(t._s(e.name))])])}),0):a("div",{staticClass:"no-payment-methods"},[a("div",{staticClass:"no-methods-text"},[t._v(t._s(t.$t("vanPull[1]")))])])]),t._v(" "),t.selectedPaymentMethod&&t.requiresBankCode?a("div",{staticClass:"bank-selection-section"},[a("div",{staticClass:"section-title"},[t._v(t._s(t.$t("globalPay.selectBank")))]),t._v(" "),a("div",{staticClass:"bank-selector",on:{click:function(e){t.showBankPicker=!0}}},[t.selectedBankCode?a("span",{staticClass:"selected-bank"},[t._v(t._s(t.selectedBankName))]):a("span",{staticClass:"placeholder"},[t._v(t._s(t.$t("globalPay.selectBank")))]),t._v(" "),a("span",{staticClass:"arrow-icon"},[t._v("▼")])])]):t._e(),t._v(" "),t.selectedPaymentMethod?a("div",{staticClass:"amount-section"},[a("div",{staticClass:"section-title"},[t._v(t._s(t.$t("recharge.info[0]")))]),t._v(" "),a("div",{staticClass:"amount-input-container"},[a("div",{staticClass:"currency-prefix"},[t._v("RP")]),t._v(" "),a("input",{directives:[{name:"model",rawName:"v-model",value:t.paymentAmount,expression:"paymentAmount"}],staticClass:"amount-input",attrs:{type:"number",placeholder:t.$t("recharge.placeholder[0]")},domProps:{value:t.paymentAmount},on:{input:[function(e){e.target.composing||(t.paymentAmount=e.target.value)},t.onAmountChange]}})]),t._v(" "),t.selectedPaymentMethod?a("div",{staticClass:"amount-range-info"},[t._v("\n        "+t._s(t.getAmountRangeText())+"\n      ")]):t._e()]):t._e(),t._v(" "),t.selectedPaymentMethod&&t.paymentAmount?a("div",{staticClass:"payment-button-section"},[a("button",{staticClass:"pay-now-button",attrs:{disabled:t.isSubmitting},on:{click:t.createPaymentOrder}},[t.isSubmitting?a("span",[t._v(t._s(t.$t("recharge.default[5]")))]):a("span",[t._v(t._s(t.$t("recharge.default[6]"))+" RP "+t._s(t.formatDisplayAmount(t.paymentAmount)))])])]):t._e()]),t._v(" "),a("van-popup",{staticClass:"PickerPopup",style:{height:"40%"},attrs:{position:"bottom"},model:{value:t.showBankPicker,callback:function(e){t.showBankPicker=e},expression:"showBankPicker"}},[a("van-picker",{attrs:{"show-toolbar":"",columns:t.bankList,title:t.$t("globalPay.selectBank")},on:{confirm:t.onBankConfirm,cancel:function(e){t.showBankPicker=!1}}})],1)],1)},staticRenderFns:[]};var o=a("VU/8")(s,i,!1,function(t){a("1iJ9")},"data-v-f1f90eca",null);e.default=o.exports},oTBK:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default={methods:{formatBalanceDisplay:function(t){return this.$Util.FormatCurrency(t,this.$i18n.locale)},formatBalanceTooltip:function(t){return this.$Util.FormatCurrencyTooltip(t,this.$i18n.locale)},formatCurrency:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.$Util.FormatCurrency(t,this.$i18n.locale,e)},formatFullCurrency:function(t){return(Number(t)||0).toLocaleString(this.$i18n.locale,{minimumFractionDigits:2,maximumFractionDigits:2})}}}}});