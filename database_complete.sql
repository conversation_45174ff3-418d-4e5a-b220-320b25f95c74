-- MySQL dump 10.13  Distrib 5.5.62, for Linux (x86_64)
--
-- Host: localhost    Database: www_xyw64_top
-- ------------------------------------------------------
-- Server version	5.5.62-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `lc3_answers`
--

DROP TABLE IF EXISTS `lc3_answers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_answers` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `department` int(10) unsigned NOT NULL DEFAULT '0',
  `lang` varchar(3) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `message` text,
  `fireup` smallint(5) unsigned NOT NULL DEFAULT '60',
  `msgtype` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1=standard,2=welcome,3=closed,4=expired,5=firstmsg',
  `created` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  PRIMARY KEY (`id`),
  KEY `depid` (`department`,`lang`,`fireup`,`msgtype`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_answers`
--

LOCK TABLES `lc3_answers` WRITE;
/*!40000 ALTER TABLE `lc3_answers` DISABLE KEYS */;
INSERT INTO `lc3_answers` VALUES (1,0,'en','Enters Chat','%operator% enters the chat.',15,2,'2021-02-04 22:21:27'),(2,0,'en','Expired','This session has expired!',15,4,'2021-02-04 22:21:27'),(3,0,'en','Ended','%client% has ended the conversation',15,3,'2021-02-04 22:21:27'),(4,0,'en','Welcome','Welcome %client%, a representative will be with you shortly.',15,5,'2021-02-04 22:21:27'),(5,0,'en','Leave','has left the conversation.',15,6,'2021-02-04 22:21:27'),(6,0,'en','Start Page','Please insert your name to begin, a representative will be with you shortly.',15,7,'2021-02-04 22:21:27'),(7,0,'en','Contact Page','None of our representatives are available right now, although you are welcome to leave a message!',15,8,'2021-02-04 22:21:27'),(8,0,'en','Feedback Page','We would appreciate your feedback to improve our service.',15,9,'2021-02-04 22:21:27'),(9,0,'en','Quickstart Page','Please type a message and hit enter to start the conversation.',15,10,'2021-02-04 22:21:27'),(10,0,'en','Group Chat Welcome Message','Welcome to our weekly support session, sharing experience and feedback.',0,11,'2021-02-04 22:21:27'),(11,0,'en','Group Chat Offline Message','The public chat is offline at this moment, please try again later.',15,12,'2021-02-04 22:21:27'),(12,0,'en','Group Chat Full Message','The public chat is full, please try again later.',15,13,'2021-02-04 22:21:27'),(13,0,'en','WhatsApp Online','Please click on a operator below to connect via WhatsApp and get help immediately.',15,26,'2021-02-04 22:21:27'),(14,0,'en','WhatsApp Offline','We are currently offline however please check below for available operators in WhatsApp, we try to help you as soon as possible.',15,27,'2021-02-04 22:21:27');
/*!40000 ALTER TABLE `lc3_answers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_autoproactive`
--

DROP TABLE IF EXISTS `lc3_autoproactive`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_autoproactive` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `path` varchar(200) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `imgpath` varchar(255) DEFAULT NULL,
  `message` varchar(255) DEFAULT NULL,
  `btn_confirm` varchar(50) DEFAULT NULL,
  `btn_cancel` varchar(50) DEFAULT NULL,
  `showalert` smallint(1) unsigned NOT NULL DEFAULT '1',
  `soundalert` varchar(100) DEFAULT NULL,
  `timeonsite` smallint(3) unsigned NOT NULL DEFAULT '2',
  `visitedsites` smallint(2) unsigned NOT NULL DEFAULT '1',
  `time` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  PRIMARY KEY (`id`),
  KEY `path` (`path`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_autoproactive`
--

LOCK TABLES `lc3_autoproactive` WRITE;
/*!40000 ALTER TABLE `lc3_autoproactive` DISABLE KEYS */;
/*!40000 ALTER TABLE `lc3_autoproactive` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_bot_question`
--

DROP TABLE IF EXISTS `lc3_bot_question`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_bot_question` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `widgetids` varchar(100) DEFAULT '0',
  `depid` int(10) unsigned NOT NULL DEFAULT '0',
  `lang` varchar(2) DEFAULT NULL,
  `question` text,
  `answer` text,
  `updated` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  `created` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  `active` tinyint(1) unsigned NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `widgetids` (`widgetids`,`depid`,`lang`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_bot_question`
--

LOCK TABLES `lc3_bot_question` WRITE;
/*!40000 ALTER TABLE `lc3_bot_question` DISABLE KEYS */;
/*!40000 ALTER TABLE `lc3_bot_question` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_buttonstats`
--

DROP TABLE IF EXISTS `lc3_buttonstats`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_buttonstats` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `depid` int(10) unsigned NOT NULL DEFAULT '0',
  `opid` int(10) unsigned NOT NULL DEFAULT '0',
  `referrer` varchar(255) DEFAULT NULL,
  `firstreferrer` varchar(255) DEFAULT NULL,
  `agent` varchar(255) DEFAULT NULL,
  `hits` int(10) NOT NULL DEFAULT '0',
  `ip` char(45) NOT NULL DEFAULT '0',
  `country` varchar(64) DEFAULT NULL,
  `countrycode` char(2) NOT NULL DEFAULT 'xx',
  `latitude` varchar(255) DEFAULT NULL,
  `longitude` varchar(255) DEFAULT NULL,
  `proactive` int(10) NOT NULL DEFAULT '0',
  `message` varchar(255) DEFAULT NULL,
  `readtime` smallint(1) NOT NULL DEFAULT '0',
  `session` varchar(64) DEFAULT NULL,
  `lasttime` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  `time` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  PRIMARY KEY (`id`),
  KEY `depid` (`depid`),
  KEY `session` (`session`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_buttonstats`
--

LOCK TABLES `lc3_buttonstats` WRITE;
/*!40000 ALTER TABLE `lc3_buttonstats` DISABLE KEYS */;
/*!40000 ALTER TABLE `lc3_buttonstats` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_chatwidget`
--

DROP TABLE IF EXISTS `lc3_chatwidget`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_chatwidget` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) DEFAULT NULL,
  `whatsapp_message` text,
  `depid` varchar(50) NOT NULL DEFAULT '0',
  `opid` int(10) unsigned NOT NULL DEFAULT '0',
  `lang` char(2) DEFAULT NULL,
  `widget` tinyint(3) unsigned NOT NULL DEFAULT '1',
  `hideoff` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `buttonimg` varchar(100) NOT NULL DEFAULT '',
  `mobilebuttonimg` varchar(100) NOT NULL DEFAULT '',
  `slideimg` varchar(100) NOT NULL DEFAULT '',
  `floatpopup` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `chat_direct` tinyint(3) unsigned NOT NULL DEFAULT '1',
  `whatsapp_online` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `whatsapp_offline` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `client_email` tinyint(3) unsigned NOT NULL DEFAULT '1',
  `client_semail` tinyint(3) unsigned NOT NULL DEFAULT '1',
  `client_phone` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `client_sphone` tinyint(3) unsigned NOT NULL DEFAULT '1',
  `client_question` tinyint(3) unsigned NOT NULL DEFAULT '1',
  `client_squestion` tinyint(3) unsigned NOT NULL DEFAULT '1',
  `show_avatar` tinyint(3) unsigned NOT NULL DEFAULT '1',
  `floatcss` varchar(100) DEFAULT NULL,
  `floatcsschat` varchar(100) DEFAULT NULL,
  `engagecss` varchar(100) DEFAULT NULL,
  `btn_animation` varchar(20) DEFAULT NULL,
  `chat_animation` varchar(20) DEFAULT NULL,
  `engage_animation` varchar(20) DEFAULT NULL,
  `dsgvo` text,
  `redirect_url` varchar(200) DEFAULT NULL,
  `redirect_active` tinyint(3) unsigned DEFAULT '0',
  `redirect_after` tinyint(3) unsigned DEFAULT '8',
  `feedback` tinyint(3) unsigned DEFAULT '1',
  `sucolor` char(7) NOT NULL DEFAULT '#6f6f6f',
  `sutcolor` char(7) NOT NULL DEFAULT '#ffffff',
  `template` varchar(20) DEFAULT NULL,
  `theme_colour` varchar(10) DEFAULT 'primary',
  `body_colour` char(7) DEFAULT '#ffffff',
  `h_colour` char(7) DEFAULT '#494949',
  `c_colour` char(7) DEFAULT '#494949',
  `time_colour` char(7) DEFAULT '#999999',
  `link_colour` char(7) DEFAULT '#2f942b',
  `sidebar_colour` char(7) DEFAULT '#857d7d',
  `t_font` varchar(100) NOT NULL DEFAULT '',
  `h_font` varchar(100) NOT NULL DEFAULT 'NonGoogle',
  `c_font` varchar(100) NOT NULL DEFAULT 'Arial, Helvetica, sans-serif',
  `widget_whitelist` text,
  `created` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  PRIMARY KEY (`id`),
  KEY `depid` (`depid`,`opid`,`lang`)
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_chatwidget`
--

LOCK TABLES `lc3_chatwidget` WRITE;
/*!40000 ALTER TABLE `lc3_chatwidget` DISABLE KEYS */;
INSERT INTO `lc3_chatwidget` VALUES (1,'Live Support Chat',NULL,'0',0,'en',1,0,'jaklc_on.png','','chatnow_on.png',1,1,0,0,1,1,0,1,1,1,1,'bottom:0;right:40px;','bottom:0;right:40px;','left:50%;top:50%;transform: translate(-50%, -50%);',NULL,NULL,NULL,NULL,NULL,0,8,1,'','','modern','standard','#ffffff','#494949','#494949','#999999','#007ff5','#857d7d','','Open+Sans','Open+Sans','','2021-02-04 22:21:30');
/*!40000 ALTER TABLE `lc3_chatwidget` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_checkstatus`
--

DROP TABLE IF EXISTS `lc3_checkstatus`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_checkstatus` (
  `convid` int(10) unsigned NOT NULL DEFAULT '0',
  `depid` int(10) unsigned NOT NULL DEFAULT '0',
  `department` varchar(100) DEFAULT NULL,
  `operatorid` int(10) unsigned NOT NULL DEFAULT '0',
  `operator` varchar(100) DEFAULT NULL,
  `pusho` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `newc` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `newo` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `files` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `knockknock` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `msgdel` int(10) unsigned NOT NULL DEFAULT '0',
  `msgedit` int(10) unsigned NOT NULL DEFAULT '0',
  `typec` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `typeo` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `transferoid` int(10) unsigned NOT NULL DEFAULT '0',
  `transferid` int(10) unsigned NOT NULL DEFAULT '0',
  `denied` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `hide` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `datac` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `statusc` int(10) unsigned NOT NULL DEFAULT '0',
  `statuso` int(10) unsigned NOT NULL DEFAULT '0',
  `initiated` int(10) unsigned NOT NULL DEFAULT '0',
  UNIQUE KEY `convid` (`convid`),
  KEY `denied` (`denied`,`hide`,`statusc`,`statuso`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_checkstatus`
--

LOCK TABLES `lc3_checkstatus` WRITE;
/*!40000 ALTER TABLE `lc3_checkstatus` DISABLE KEYS */;
INSERT INTO `lc3_checkstatus` VALUES (1,1,'My First Department',1,'系统',1,0,0,0,0,0,0,0,0,0,0,0,1,0,1612452444,1612451669,1612451147);
/*!40000 ALTER TABLE `lc3_checkstatus` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_clientcontact`
--

DROP TABLE IF EXISTS `lc3_clientcontact`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_clientcontact` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `sessionid` int(10) unsigned NOT NULL DEFAULT '0',
  `operatorid` int(10) unsigned NOT NULL DEFAULT '0',
  `operatorname` varchar(255) DEFAULT NULL,
  `subject` varchar(255) DEFAULT NULL,
  `message` text,
  `sent` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_clientcontact`
--

LOCK TABLES `lc3_clientcontact` WRITE;
/*!40000 ALTER TABLE `lc3_clientcontact` DISABLE KEYS */;
/*!40000 ALTER TABLE `lc3_clientcontact` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_contacts`
--

DROP TABLE IF EXISTS `lc3_contacts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_contacts` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `depid` int(10) unsigned NOT NULL DEFAULT '0',
  `name` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `message` text,
  `ip` char(45) DEFAULT NULL,
  `country` varchar(64) DEFAULT NULL,
  `city` varchar(64) DEFAULT NULL,
  `countrycode` varchar(2) DEFAULT NULL,
  `latitude` varchar(255) DEFAULT NULL,
  `longitude` varchar(255) DEFAULT NULL,
  `referrer` varchar(255) DEFAULT NULL,
  `reply` smallint(1) unsigned NOT NULL DEFAULT '0',
  `answered` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  `sent` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  PRIMARY KEY (`id`),
  KEY `depid` (`depid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_contacts`
--

LOCK TABLES `lc3_contacts` WRITE;
/*!40000 ALTER TABLE `lc3_contacts` DISABLE KEYS */;
/*!40000 ALTER TABLE `lc3_contacts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_contactsreply`
--

DROP TABLE IF EXISTS `lc3_contactsreply`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_contactsreply` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `contactid` int(10) unsigned NOT NULL DEFAULT '0',
  `operatorid` int(10) unsigned NOT NULL DEFAULT '0',
  `operatorname` varchar(255) DEFAULT NULL,
  `subject` varchar(255) DEFAULT NULL,
  `message` text,
  `sent` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  PRIMARY KEY (`id`),
  KEY `contactid` (`contactid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_contactsreply`
--

LOCK TABLES `lc3_contactsreply` WRITE;
/*!40000 ALTER TABLE `lc3_contactsreply` DISABLE KEYS */;
/*!40000 ALTER TABLE `lc3_contactsreply` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_departments`
--

DROP TABLE IF EXISTS `lc3_departments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_departments` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) DEFAULT NULL,
  `description` mediumtext,
  `email` varchar(255) DEFAULT NULL,
  `faq_url` text,
  `active` smallint(1) unsigned NOT NULL DEFAULT '1',
  `dorder` smallint(2) unsigned NOT NULL DEFAULT '1',
  `time` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_departments`
--

LOCK TABLES `lc3_departments` WRITE;
/*!40000 ALTER TABLE `lc3_departments` DISABLE KEYS */;
INSERT INTO `lc3_departments` VALUES (1,'My First Department','Edit this department to your needs...',NULL,NULL,1,1,'2021-02-04 22:21:34');
/*!40000 ALTER TABLE `lc3_departments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_files`
--

DROP TABLE IF EXISTS `lc3_files`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_files` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `path` text,
  `name` varchar(200) DEFAULT NULL,
  `description` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_files`
--

LOCK TABLES `lc3_files` WRITE;
/*!40000 ALTER TABLE `lc3_files` DISABLE KEYS */;
/*!40000 ALTER TABLE `lc3_files` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_groupchat`
--

DROP TABLE IF EXISTS `lc3_groupchat`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_groupchat` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `password` varchar(20) DEFAULT NULL,
  `title` varchar(100) DEFAULT NULL,
  `description` text,
  `opids` varchar(10) DEFAULT '0',
  `maxclients` tinyint(3) unsigned NOT NULL DEFAULT '20',
  `lang` char(2) DEFAULT NULL,
  `buttonimg` varchar(100) NOT NULL DEFAULT '',
  `floatpopup` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `floatcss` varchar(100) DEFAULT NULL,
  `active` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `created` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  PRIMARY KEY (`id`),
  KEY `opids` (`opids`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_groupchat`
--

LOCK TABLES `lc3_groupchat` WRITE;
/*!40000 ALTER TABLE `lc3_groupchat` DISABLE KEYS */;
INSERT INTO `lc3_groupchat` VALUES (1,NULL,'Weekly Support',NULL,'0',10,'en','colour_on.png',0,'bottom:20px;left:20px',0,'2021-02-04 22:21:35');
/*!40000 ALTER TABLE `lc3_groupchat` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_groupchatmsg`
--

DROP TABLE IF EXISTS `lc3_groupchatmsg`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_groupchatmsg` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `groupchatid` int(10) NOT NULL DEFAULT '0',
  `chathistory` mediumtext,
  `operatorid` int(10) unsigned NOT NULL DEFAULT '0',
  `created` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  PRIMARY KEY (`id`),
  KEY `groupchatid` (`groupchatid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_groupchatmsg`
--

LOCK TABLES `lc3_groupchatmsg` WRITE;
/*!40000 ALTER TABLE `lc3_groupchatmsg` DISABLE KEYS */;
/*!40000 ALTER TABLE `lc3_groupchatmsg` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_groupchatuser`
--

DROP TABLE IF EXISTS `lc3_groupchatuser`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_groupchatuser` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `groupchatid` int(10) NOT NULL DEFAULT '0',
  `name` varchar(100) DEFAULT NULL,
  `usr_avatar` varchar(255) DEFAULT NULL,
  `statusc` int(10) unsigned NOT NULL DEFAULT '0',
  `lastmsg` int(10) unsigned NOT NULL DEFAULT '0',
  `banned` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `ip` char(45) NOT NULL DEFAULT '0',
  `isop` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `session` varchar(64) DEFAULT NULL,
  `created` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  PRIMARY KEY (`id`),
  KEY `groupchatid` (`groupchatid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_groupchatuser`
--

LOCK TABLES `lc3_groupchatuser` WRITE;
/*!40000 ALTER TABLE `lc3_groupchatuser` DISABLE KEYS */;
/*!40000 ALTER TABLE `lc3_groupchatuser` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_loginlog`
--

DROP TABLE IF EXISTS `lc3_loginlog`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_loginlog` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `fromwhere` varchar(255) DEFAULT NULL,
  `ip` char(45) NOT NULL DEFAULT '0',
  `usragent` varchar(255) DEFAULT NULL,
  `time` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  `access` smallint(1) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_loginlog`
--

LOCK TABLES `lc3_loginlog` WRITE;
/*!40000 ALTER TABLE `lc3_loginlog` DISABLE KEYS */;
INSERT INTO `lc3_loginlog` VALUES (1,'zz','/kefu/operator/','127.0.0.1','Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36 FS','2021-02-04 15:31:51',1);
/*!40000 ALTER TABLE `lc3_loginlog` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_operatorchat`
--

DROP TABLE IF EXISTS `lc3_operatorchat`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_operatorchat` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `fromid` int(10) NOT NULL DEFAULT '0',
  `toid` int(10) NOT NULL DEFAULT '0',
  `message` text,
  `sent` int(10) NOT NULL DEFAULT '0',
  `received` smallint(1) unsigned NOT NULL DEFAULT '0',
  `msgpublic` smallint(1) unsigned NOT NULL DEFAULT '0',
  `system_message` varchar(3) DEFAULT 'no',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_operatorchat`
--

LOCK TABLES `lc3_operatorchat` WRITE;
/*!40000 ALTER TABLE `lc3_operatorchat` DISABLE KEYS */;
/*!40000 ALTER TABLE `lc3_operatorchat` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_push_notification_devices`
--

DROP TABLE IF EXISTS `lc3_push_notification_devices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_push_notification_devices` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `userid` int(10) unsigned NOT NULL DEFAULT '0',
  `ostype` enum('ios','android') NOT NULL DEFAULT 'ios',
  `token` varchar(255) DEFAULT NULL,
  `lastedit` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  `created` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  PRIMARY KEY (`id`),
  KEY `userid` (`userid`,`ostype`,`token`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_push_notification_devices`
--

LOCK TABLES `lc3_push_notification_devices` WRITE;
/*!40000 ALTER TABLE `lc3_push_notification_devices` DISABLE KEYS */;
/*!40000 ALTER TABLE `lc3_push_notification_devices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_responses`
--

DROP TABLE IF EXISTS `lc3_responses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_responses` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `department` int(10) unsigned NOT NULL DEFAULT '0',
  `title` varchar(200) DEFAULT NULL,
  `message` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_responses`
--

LOCK TABLES `lc3_responses` WRITE;
/*!40000 ALTER TABLE `lc3_responses` DISABLE KEYS */;
INSERT INTO `lc3_responses` VALUES (1,0,'Assist Today','How can I assist you today?');
/*!40000 ALTER TABLE `lc3_responses` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_sessions`
--

DROP TABLE IF EXISTS `lc3_sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_sessions` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `userid` varchar(200) DEFAULT NULL,
  `department` int(10) unsigned NOT NULL DEFAULT '0',
  `operatorid` int(10) unsigned NOT NULL DEFAULT '0',
  `operatorname` varchar(255) DEFAULT NULL,
  `template` varchar(20) DEFAULT NULL,
  `usr_avatar` varchar(255) DEFAULT NULL,
  `name` varchar(100) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(100) DEFAULT NULL,
  `country` varchar(64) DEFAULT NULL,
  `city` varchar(64) DEFAULT NULL,
  `countrycode` varchar(2) DEFAULT NULL,
  `latitude` varchar(255) DEFAULT NULL,
  `longitude` varchar(255) DEFAULT NULL,
  `notes` text,
  `status` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `fcontact` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `initiated` int(10) unsigned NOT NULL DEFAULT '0',
  `ended` int(10) unsigned NOT NULL DEFAULT '0',
  `deniedoid` int(10) unsigned NOT NULL DEFAULT '0',
  `session` varchar(64) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `userid` (`userid`),
  KEY `operatorid` (`operatorid`),
  KEY `session` (`session`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_sessions`
--

LOCK TABLES `lc3_sessions` WRITE;
/*!40000 ALTER TABLE `lc3_sessions` DISABLE KEYS */;
INSERT INTO `lc3_sessions` VALUES (1,'0777694',1,1,'系统','modern','/package/modern/avatar/2.jpg','15555555556','<EMAIL>','9876543210','Disabled','Disabled','xx','','',NULL,0,0,1612451146,1612451670,0,'749561612451146');
/*!40000 ALTER TABLE `lc3_sessions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_settings`
--

DROP TABLE IF EXISTS `lc3_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_settings` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `varname` varchar(100) DEFAULT NULL,
  `used_value` text,
  `default_value` text,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=54 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_settings`
--

LOCK TABLES `lc3_settings` WRITE;
/*!40000 ALTER TABLE `lc3_settings` DISABLE KEYS */;
INSERT INTO `lc3_settings` VALUES (1,'allowedo_files','.zip,.rar,.jpg,.jpeg,.png,.gif','.zip,.rar,.jpg,.jpeg,.png,.gif'),(2,'allowed_files','.zip,.rar,.jpg,.jpeg,.png,.gif','.zip,.rar,.jpg,.jpeg,.png,.gif'),(3,'validtill','0','0'),(4,'captcha','0','1'),(5,'client_expired','600','600'),(6,'client_left','300','300'),(7,'crating','1','0'),(8,'dateformat','d.m.Y','d.m.Y'),(9,'email','<EMAIL>','@jakbusiness'),(10,'emailcc','','@jakcc'),(11,'email_block','',NULL),(12,'facebook','',''),(13,'facebook_big','',''),(14,'ip_block','',NULL),(15,'lang','en','en'),(16,'live_online_status','0','0'),(17,'msg_tone','new_message','new_message'),(18,'openop','1','1'),(19,'o_number','10020200','0'),(20,'pro_alert','1','1'),(21,'ring_tone','ring','ring'),(22,'send_tscript','1','1'),(23,'show_ips','1','1'),(24,'smtphost','',''),(25,'smtppassword','',''),(26,'smtpport','25','25'),(27,'smtpusername','',''),(28,'smtp_alive','0','0'),(29,'smtp_auth','0','0'),(30,'smtp_mail','0','0'),(31,'smtp_prefix','',''),(32,'timeformat','g:i a','g:i a'),(33,'timezoneserver','Europe/Zurich','Europe/Zurich'),(34,'title',' ',' '),(35,'twilio_nexmo','0','1'),(36,'twitter','',''),(37,'twitter_big','',''),(38,'tw_msg','A customer is requesting attention.','A customer is requesting attention.'),(39,'tw_phone','',''),(40,'tw_sid','',''),(41,'tw_token','',''),(42,'updated','1612448498','1475494685'),(43,'useravatheight','113','113'),(44,'useravatwidth','150','150'),(45,'version','3.8.2','3.8.2'),(46,'holiday_mode','0','0'),(47,'push_reminder','120','120'),(48,'native_app_token','','jakweb_app'),(49,'native_app_key','','jakweb_app'),(50,'client_push_not','1','1'),(51,'engage_sound','sound/new_message3','sound/new_message3'),(52,'client_sound','sound/hello','sound/hello'),(53,'proactive_time','3','3');
/*!40000 ALTER TABLE `lc3_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_transcript`
--

DROP TABLE IF EXISTS `lc3_transcript`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_transcript` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  `message` varchar(2000) DEFAULT NULL,
  `user` varchar(100) DEFAULT NULL,
  `operatorid` int(10) unsigned NOT NULL DEFAULT '0',
  `convid` int(10) unsigned NOT NULL DEFAULT '0',
  `quoted` int(10) unsigned NOT NULL DEFAULT '0',
  `replied` int(10) unsigned NOT NULL DEFAULT '0',
  `starred` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `editoid` int(10) unsigned NOT NULL DEFAULT '0',
  `edited` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  `time` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  `sentstatus` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `class` varchar(20) DEFAULT NULL,
  `plevel` smallint(1) unsigned NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `convid` (`convid`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_transcript`
--

LOCK TABLES `lc3_transcript` WRITE;
/*!40000 ALTER TABLE `lc3_transcript` DISABLE KEYS */;
INSERT INTO `lc3_transcript` VALUES (1,'System','Welcome 15555555556, a representative will be with you shortly.',NULL,0,1,0,0,0,0,'1980-05-06 00:00:00','2021-02-04 16:05:47',1,'notice',1),(2,'15555555556','大女当嫁单',NULL,0,1,0,0,0,0,'1980-05-06 00:00:00','2021-02-04 16:05:48',1,'user',1),(3,'15555555556','111','0777694',0,1,0,0,0,0,'1980-05-06 00:00:00','2021-02-04 16:06:15',1,'user',1),(4,'系统','系统 enters the chat.','1::zz',1,1,0,0,0,0,'1980-05-06 00:00:00','2021-02-04 16:08:55',1,'admin',1),(5,'系统','111','1::zz',1,1,0,0,0,0,'1980-05-06 00:00:00','2021-02-04 16:09:22',1,'admin',1),(6,'15555555556','This session has expired!',NULL,0,1,0,0,0,0,'1980-05-06 00:00:00','2021-02-04 16:27:24',0,'ended',1);
/*!40000 ALTER TABLE `lc3_transcript` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_transfer`
--

DROP TABLE IF EXISTS `lc3_transfer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_transfer` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `convid` int(10) unsigned NOT NULL DEFAULT '0',
  `fromoid` int(10) unsigned NOT NULL DEFAULT '0',
  `fromname` varchar(100) DEFAULT NULL,
  `tooid` int(10) unsigned NOT NULL DEFAULT '0',
  `toname` varchar(100) DEFAULT NULL,
  `message` text,
  `used` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `created` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  PRIMARY KEY (`id`),
  KEY `convid` (`convid`,`tooid`,`used`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_transfer`
--

LOCK TABLES `lc3_transfer` WRITE;
/*!40000 ALTER TABLE `lc3_transfer` DISABLE KEYS */;
/*!40000 ALTER TABLE `lc3_transfer` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_urlblacklist`
--

DROP TABLE IF EXISTS `lc3_urlblacklist`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_urlblacklist` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `path` varchar(200) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `time` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  PRIMARY KEY (`id`),
  KEY `path` (`path`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_urlblacklist`
--

LOCK TABLES `lc3_urlblacklist` WRITE;
/*!40000 ALTER TABLE `lc3_urlblacklist` DISABLE KEYS */;
/*!40000 ALTER TABLE `lc3_urlblacklist` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_user`
--

DROP TABLE IF EXISTS `lc3_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_user` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `departments` varchar(100) DEFAULT '0',
  `available` smallint(1) unsigned NOT NULL DEFAULT '0',
  `busy` smallint(1) unsigned NOT NULL DEFAULT '0',
  `hours_array` text,
  `phonenumber` varchar(255) DEFAULT NULL,
  `whatsappnumber` varchar(255) DEFAULT NULL,
  `pusho_tok` varchar(50) DEFAULT NULL,
  `pusho_key` varchar(50) DEFAULT NULL,
  `username` varchar(100) DEFAULT NULL,
  `password` char(64) DEFAULT NULL,
  `idhash` varchar(32) DEFAULT NULL,
  `session` varchar(64) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `picture` varchar(100) NOT NULL DEFAULT '/standard.jpg',
  `language` varchar(10) DEFAULT NULL,
  `invitationmsg` varchar(255) DEFAULT NULL,
  `time` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  `lastactivity` int(10) unsigned NOT NULL DEFAULT '0',
  `hits` int(10) unsigned NOT NULL DEFAULT '0',
  `logins` int(10) unsigned NOT NULL DEFAULT '0',
  `responses` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `files` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `useronlinelist` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `operatorchat` tinyint(1) NOT NULL DEFAULT '0',
  `operatorchatpublic` tinyint(1) NOT NULL DEFAULT '1',
  `operatorlist` tinyint(1) NOT NULL DEFAULT '0',
  `transferc` tinyint(1) NOT NULL DEFAULT '1',
  `chat_latency` smallint(4) unsigned NOT NULL DEFAULT '3000',
  `push_notifications` tinyint(1) NOT NULL DEFAULT '1',
  `sound` tinyint(1) NOT NULL DEFAULT '1',
  `ringing` tinyint(2) NOT NULL DEFAULT '3',
  `alwaysnot` tinyint(1) NOT NULL DEFAULT '0',
  `emailnot` tinyint(1) NOT NULL DEFAULT '0',
  `access` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `permissions` varchar(512) DEFAULT NULL,
  `forgot` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_user`
--

LOCK TABLES `lc3_user` WRITE;
/*!40000 ALTER TABLE `lc3_user` DISABLE KEYS */;
INSERT INTO `lc3_user` VALUES (1,'0',0,0,'','','','','','zz','0b0f8c86c0e5404a17b83ef92d2acdcc262154f2d87b53be957ec2485b76eb36','c56c11e57e8e41de9f4d9a8bf83372c1','pmqh5sot0469j5gokcvb543ome','<EMAIL>','系统','/standard.jpg','','','2021-02-04 15:28:26',1612452126,0,1,1,1,1,1,1,0,1,3000,1,1,3,0,0,1,'',0),(2,'0',1,0,NULL,NULL,NULL,NULL,NULL,'admin','794386a210ef34da267497d98c88e1848d5de3a87a6c3489b578f1e6822fba88','8df0ff65da6e1a763051b7ad6e012e22','gef1193cq97tujg3namj5tgcjc','','admin','/standard.jpg',NULL,NULL,'2021-02-05 20:33:59',1734067374,0,0,1,1,1,1,1,0,1,3000,1,1,3,0,0,1,NULL,0);
/*!40000 ALTER TABLE `lc3_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lc3_user_stats`
--

DROP TABLE IF EXISTS `lc3_user_stats`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lc3_user_stats` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `userid` int(10) unsigned NOT NULL DEFAULT '0',
  `vote` int(10) unsigned NOT NULL DEFAULT '0',
  `name` varchar(100) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `comment` text,
  `support_time` int(10) unsigned NOT NULL DEFAULT '0',
  `time` datetime NOT NULL DEFAULT '1980-05-06 00:00:00',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lc3_user_stats`
--

LOCK TABLES `lc3_user_stats` WRITE;
/*!40000 ALTER TABLE `lc3_user_stats` DISABLE KEYS */;
/*!40000 ALTER TABLE `lc3_user_stats` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_actionlog`
--

DROP TABLE IF EXISTS `ly_actionlog`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_actionlog` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` char(30) NOT NULL DEFAULT '' COMMENT '用户名',
  `ip` char(15) NOT NULL DEFAULT '' COMMENT '操作IP',
  `time` int(11) NOT NULL DEFAULT '0' COMMENT '操作时间',
  `log` text NOT NULL DEFAULT '' COMMENT '操作详情',
  `isadmin` tinyint(4) NOT NULL DEFAULT '2' COMMENT '类型。\r\n1：运营后台；\r\n2：前台；\r\n3:商户后台;',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `time` (`time`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='操作日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_actionlog`
--

LOCK TABLES `ly_actionlog` WRITE;
/*!40000 ALTER TABLE `ly_actionlog` DISABLE KEYS */;
INSERT INTO `ly_actionlog` VALUES (1,'admin','**************',1734172473,'添加任务：标题为Browse',1);
/*!40000 ALTER TABLE `ly_actionlog` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_activity_list`
--

DROP TABLE IF EXISTS `ly_activity_list`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_activity_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` char(30) NOT NULL DEFAULT '' COMMENT '活动标题',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '活动简述',
  `cover_img` varchar(100) NOT NULL DEFAULT '' COMMENT '活动封面图',
  `start_time` int(11) NOT NULL DEFAULT '0' COMMENT '活动开始时间',
  `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '活动结束时间',
  `sort` smallint(9) NOT NULL DEFAULT '0' COMMENT '活动排序',
  `state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '活动状态。\r\n1：开启；\r\n2：关闭；',
  `explain` text NOT NULL DEFAULT '' COMMENT '活动说明',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `sort` (`state`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='活动列表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_activity_list`
--

LOCK TABLES `ly_activity_list` WRITE;
/*!40000 ALTER TABLE `ly_activity_list` DISABLE KEYS */;
INSERT INTO `ly_activity_list` VALUES (1,'1','1','/upload/image/202010281700519487253115.jpg',1606579200,1609344000,1,1,'<p>111111111111111</p>');
/*!40000 ALTER TABLE `ly_activity_list` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_admin_role`
--

DROP TABLE IF EXISTS `ly_admin_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_admin_role` (
  `id` int(8) NOT NULL AUTO_INCREMENT,
  `role_name` varchar(60) NOT NULL DEFAULT '' COMMENT '权限',
  `role_url` varchar(100) NOT NULL DEFAULT '' COMMENT '权限',
  `basic` tinyint(4) NOT NULL DEFAULT '2' COMMENT '基本商户',
  `agent` tinyint(4) NOT NULL DEFAULT '2' COMMENT '代理商户',
  `user` tinyint(4) NOT NULL DEFAULT '2' COMMENT '码商\r\n1:启用;\r\n2:关闭;',
  `sort` tinyint(4) NOT NULL DEFAULT '1',
  `level` tinyint(4) NOT NULL DEFAULT '1',
  `pid` int(8) NOT NULL DEFAULT '1' COMMENT '客服版状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=42 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_admin_role`
--

LOCK TABLES `ly_admin_role` WRITE;
/*!40000 ALTER TABLE `ly_admin_role` DISABLE KEYS */;
INSERT INTO `ly_admin_role` VALUES (1,'平台信息','Info/index',1,2,2,1,1,0),(2,'订单明细','Order/index',1,1,2,20,1,0),(3,'历史明细','Detailed/index',1,2,2,21,1,0),(4,'平台提现','Withdraw/index',1,1,2,24,1,0),(5,'平台概况','Survey/index',1,2,2,25,1,0),(6,'绑定手机','Info/bindPhone',1,2,2,1,2,1),(7,'绑定邮箱','info/bindMail',1,2,2,2,2,1),(8,'生成APIKEY','Info/setApiKey',1,2,2,3,2,1),(9,'查看APIKEY','Info/lookApiKey',1,2,2,4,2,1),(10,'修改密码','Info/editPwd',1,2,2,5,2,1),(11,'设置交易密码','Info/setPayPwd',1,2,2,6,2,1),(12,'绑定银行卡','Info/bindBankcard',1,2,2,7,2,1),(13,'订单详情','Order/orderDetailed',1,1,2,1,2,2),(14,'提现','withdraw/withdrawSub',1,1,2,1,2,4),(15,'资质认证','Info/verifySub',1,2,2,8,2,1),(16,'资料下载','Download/index',1,2,2,127,1,0),(17,'查看信息','Info/lookMe',1,2,2,10,2,1),(18,'基本信息','Baseinfo/index',2,1,2,15,1,0),(19,'代理中心','Agentcentre/index',2,1,2,16,1,0),(20,'每日报表','Everyday/index',1,1,2,17,1,0),(23,'调整费率','Baseinfo/feeEdit',2,1,2,2,2,18),(22,'绑定银行卡','Baseinfo/bindBankcard',1,1,2,1,2,18),(24,'添加商户','Agentcentre/merAdd',2,1,2,1,2,19),(25,'编辑商户','Agentcentre/merEdit',2,1,2,2,2,19),(26,'查看商户','Agentcentre/merLook',2,1,2,3,2,19),(27,'绑定手机','Baseinfo/bindPhone',1,1,2,3,2,18),(28,'下载','Download/download',1,2,2,1,2,16),(29,'回调','Order/callBack',1,1,2,2,2,2),(30,'修改密码','Baseinfo/editPwd',2,1,2,4,2,18),(31,'设置交易密码','Baseinfo/setPayPwd',2,1,2,5,2,18),(32,'订单管理','Team/order',2,2,1,3,1,1),(33,'团队成员','Team/teamInfo',2,2,1,2,1,1),(34,'团队流水','Team/teamTrade',2,2,1,4,1,1),(35,'成员二维码','Team/userQrcode',2,2,1,1,2,33),(36,'订单回调','Team/callBack',2,2,1,1,2,32),(37,'补单','Team/repairorder',2,2,1,2,2,32),(38,'二维码锁定','Team/qrcodeStatus',2,2,1,2,2,33),(39,'二维码禁用','Team/qrcodeDisabled',2,2,1,3,2,33),(40,'团队报表','Team/teamReport',2,2,1,10,1,1),(41,'每日报表','Team/teamDaily',2,2,1,10,1,1);
/*!40000 ALTER TABLE `ly_admin_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_alipay`
--

DROP TABLE IF EXISTS `ly_alipay`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_alipay` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT '0',
  `account` varchar(50) NOT NULL DEFAULT '',
  `nickname` varchar(50) NOT NULL DEFAULT '',
  `codeimg` varchar(255) NOT NULL DEFAULT '',
  `remark` varchar(255) NOT NULL DEFAULT '',
  `addtime` int(11) NOT NULL DEFAULT '0',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态。1正常；3删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_alipay`
--

LOCK TABLES `ly_alipay` WRITE;
/*!40000 ALTER TABLE `ly_alipay` DISABLE KEYS */;
/*!40000 ALTER TABLE `ly_alipay` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_bank`
--

DROP TABLE IF EXISTS `ly_bank`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_bank` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bank_name` char(30) NOT NULL DEFAULT '' COMMENT '银行名称',
  `bank_code` char(50) NOT NULL DEFAULT '' COMMENT '直连代码',
  `c_start_time` time NOT NULL DEFAULT '00:00:00' COMMENT '充值开始时间',
  `c_end_time` time NOT NULL DEFAULT '23:59:59' COMMENT '充值结束时间',
  `c_min` int(11) NOT NULL DEFAULT '0' COMMENT '最小充值金额',
  `c_max` int(11) NOT NULL DEFAULT '100000' COMMENT '最大充值金额',
  `q_start_time` time NOT NULL DEFAULT '00:00:00' COMMENT '取款开始时间',
  `q_end_time` time NOT NULL DEFAULT '23:59:59' COMMENT '取款结束时间',
  `q_min` int(11) NOT NULL DEFAULT '100' COMMENT '最小取款金额',
  `q_max` int(11) NOT NULL DEFAULT '100000' COMMENT '最大取款金额',
  `c_state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '充值状态\r\n1：开启；\r\n2：关闭；',
  `q_state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '取款状态。\r\n1：开启；\r\n2：关闭；',
  `pay_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '充值渠道ID',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=3657 DEFAULT CHARSET=utf8 ROW_FORMAT=FIXED COMMENT='银行配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_bank`
--

LOCK TABLES `ly_bank` WRITE;
/*!40000 ALTER TABLE `ly_bank` DISABLE KEYS */;
INSERT INTO `ly_bank` VALUES (109,'spain bank','BANK_CARD-B2C-POST-P2P','00:09:00','23:56:59',5,100000,'00:00:02','20:52:33',5,50000,2,2,4,**********),(110,'工商银行','BANK_CARD-B2C-ICBC-P2P','00:00:00','23:59:59',5,100000,'10:00:00','20:50:16',5,50000,2,2,4,**********),(111,'农业银行','BANK_CARD-B2C-ABC-P2P','00:00:00','23:59:59',5,100000,'10:00:00','20:50:33',5,50000,2,2,4,**********),(112,'建设银行','BANK_CARD-B2C-CCB-P2P','00:00:00','23:59:59',5,100000,'10:00:00','20:50:42',5,50000,2,2,4,**********),(113,'招商银行','BANK_CARD-B2C-CMBCHINA-P2P','00:00:00','23:59:59',5,100000,'10:00:00','20:50:57',5,50000,2,2,4,**********),(114,'中国交通银行','BANK_CARD-B2C-BOCO-P2P','00:00:00','23:59:58',5,100000,'10:00:00','20:51:29',5,50000,2,2,4,**********),(115,'中国光大银行','BANK_CARD-B2C-CEB-P2P','00:00:00','23:59:59',5,100000,'10:00:00','20:51:46',5,50000,2,2,4,**********),(116,'中国银行','BANK_CARD-B2C-BOC-P2P','00:00:00','23:59:59',5,100000,'10:00:00','20:51:58',5,50000,2,2,4,**********),(117,'中国民生银行','BANK_CARD-B2C-CMBC-P2P','00:00:00','23:59:59',5,100000,'10:00:00','20:51:52',5,50000,2,2,4,**********),(118,'中信银行','BANK_CARD-B2C-ECITIC-P2P','00:00:00','23:59:59',5,100000,'10:00:00','20:52:09',5,50000,2,2,4,**********),(119,'兴业银行','BANK_CARD-B2C-CIB-P2P','00:00:00','23:59:59',5,100000,'10:00:00','20:52:16',5,50000,2,2,4,**********),(120,'北京银行','BANK_CARD-B2C-BCCB-P2P','00:00:00','23:59:59',5,100000,'10:00:00','20:52:22',5,50000,2,2,4,**********),(121,'平安银行','BANK_CARD-B2C-PINGANBANK-P2P','00:00:00','23:59:59',5,100000,'10:00:00','20:50:06',5,50000,2,2,4,**********),(122,'上海银行','BANK_CARD-B2C-SHB-P2P','00:00:00','23:59:59',5,100000,'10:00:00','20:53:31',5,50000,2,2,4,**********),(123,'上海浦东发展银行','BANK_CARD-B2C-SPDB-P2P','00:00:00','23:59:59',5,100000,'10:00:00','20:53:41',5,50000,2,2,4,**********),(124,'广发银行','BANK_CARD-B2C-GDB-P2P','00:00:00','23:59:59',5,100000,'10:00:00','20:53:47',5,50000,2,2,4,**********),(125,'中国华夏银行','BANK_CARD-B2C-HXB-P2P','00:00:00','23:59:59',5,100000,'10:00:00','20:49:32',5,50000,2,2,4,**********),(126,'深圳发展银行','BANK_CARD-B2C-SDB-P2P','00:00:00','23:59:59',5,100000,'10:00:00','20:49:48',5,50000,2,2,4,**********),(158,'微信','weixin','00:00:00','23:59:59',5,3000,'10:00:00','02:00:00',5,50000,2,1,16,**********),(159,'QQ钱包','tenpay','00:00:00','00:00:00',5,3000,'10:00:00','02:00:00',5,50000,1,1,16,**********),(160,'支付宝','alipay','00:00:00','00:00:00',5,3000,'10:00:00','02:00:00',5,50000,1,1,16,**********),(161,'百度钱包','baidupay','00:00:00','00:00:00',5,3000,'10:00:00','02:00:00',5,50000,2,1,16,**********),(162,'京东钱包','jdpay','00:00:00','00:00:00',5,3000,'10:00:00','02:00:00',5,50000,1,1,16,**********),(163,'银联','unionpay','00:00:00','00:00:00',5,3000,'10:00:00','02:00:00',5,50000,1,1,16,**********),(164,'QQ网页版','qqwap','00:00:00','00:00:00',5,3000,'10:00:00','02:00:00',5,50000,2,1,16,**********),(165,'微信网页版','weixinwap','00:00:00','00:00:00',5,3000,'10:00:00','02:00:00',5,50000,2,1,16,**********),(166,'快捷','quick','00:00:00','00:00:00',5,3000,'10:00:00','02:00:00',5,50000,1,1,16,1513325243),(167,'上海农商银行','SHHNSYH','00:00:00','00:00:00',5,50000,'00:00:00','23:57:00',5,50000,2,2,4,**********),(326,'支付宝','alipay','00:00:00','23:59:59',5,10000,'00:00:00','23:59:59',5,10000,1,1,60,1554538670),(327,'支付宝','alipay','00:00:00','23:59:59',5,5000,'00:00:00','23:59:59',5,5000,1,1,62,1554633785),(328,'支付宝','alipay','00:00:00','23:59:59',5,5000,'00:00:00','23:59:59',5,5000,1,1,61,1554633829),(329,'支付宝','alipay2','00:00:00','00:00:00',5,10000,'00:00:00','00:00:00',5,100000,1,1,64,1556884623),(330,'支付宝','alipayquick','00:00:00','00:00:00',5,10000,'00:00:00','00:00:00',5,100000,1,1,66,1556884663),(331,'支付宝','alipay','00:00:00','00:00:00',5,10000,'00:00:00','00:00:00',5,100000,1,1,63,1556884694),(332,'微信','weixin','00:00:00','00:00:00',5,10000,'00:00:00','00:00:00',5,100000,1,1,65,1556884725),(334,'微信','weixin','00:00:00','23:59:59',5,10000,'00:00:00','23:59:59',5,10000,1,1,67,1557576576),(3652,'银联','yinlian','00:00:00','00:00:00',5,1234,'00:00:00','00:00:00',5,1234,1,1,114,1592986883),(3648,'支付宝','8017','00:00:00','00:00:00',5,1234,'00:00:00','00:00:00',5,1234,2,2,112,1592984757),(3651,'微信赞赏码','weixin2','00:00:00','00:00:00',5,1234,'00:00:00','00:00:00',5,1234,2,2,113,1592984948),(3644,'云闪付','ysf','00:00:00','00:00:00',5,1234,'00:00:00','00:00:00',5,1234,2,2,108,1592897527),(3647,'微信','8002','00:00:00','00:00:00',5,1234,'00:00:00','00:00:00',5,1234,2,2,111,1592984665),(3646,'支付宝','8007','00:00:00','00:00:00',5,1234,'00:00:00','00:00:00',5,1234,2,2,110,1592984601),(3642,'支付宝','alipay','00:00:00','00:00:00',5,1234,'00:00:00','00:00:00',5,1234,2,2,108,**********),(3650,'支付宝转卡','alipay2','00:00:00','00:00:00',5,1234,'00:00:00','00:00:00',5,1234,2,2,113,**********),(3645,'支付宝','8006','00:00:00','00:00:00',5,1234,'00:00:00','00:00:00',5,1234,2,2,109,**********),(3649,'支付宝','alipay','00:00:00','00:00:00',5,1234,'00:00:00','00:00:00',5,1234,2,2,113,**********),(3643,'微信','weixin','00:00:00','00:00:00',5,1234,'00:00:00','00:00:00',5,1234,2,2,108,**********),(3653,'充值请联系在线客服','BANK_CARD-B2C-POST-P2P','10:00:00','22:00:00',5,50000,'10:00:00','22:00:00',5,30000,2,2,4,**********),(3654,'中国农商银行','BANK_CARD-B2C-POST-P2P','00:00:00','23:59:59',5,50000,'10:00:00','22:00:00',5,50000,2,2,4,**********),(3655,'中国工商','BANK_CARD-B2C-POST-P2P','05:05:05','14:30:21',5,10000,'01:02:02','23:30:57',5,1000000,2,2,4,**********),(3656,'USDT钱包银行','USDT','00:00:00','23:59:00',5,100000,'00:00:00','23:59:00',5,100000,1,1,4,**********);
/*!40000 ALTER TABLE `ly_bank` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_data_top`
--

DROP TABLE IF EXISTS `ly_data_top`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_data_top` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户',
  `order_number` varchar(25) NOT NULL DEFAULT '' COMMENT '订单号',
  `order_type` tinyint(4) DEFAULT NULL COMMENT '类型',
  `price` decimal(16,4) NOT NULL DEFAULT '0.0000' COMMENT '奖金',
  `datatime` int(11) NOT NULL DEFAULT '0' COMMENT '用户名',
  `username` varchar(60) DEFAULT NULL,
  `lottery_type` varchar(60) DEFAULT NULL COMMENT '彩种',
  `lottery_name` varchar(60) DEFAULT NULL COMMENT '彩种名',
  `addtime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_data_top`
--

LOCK TABLES `ly_data_top` WRITE;
/*!40000 ALTER TABLE `ly_data_top` DISABLE KEYS */;
/*!40000 ALTER TABLE `ly_data_top` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_draw_config`
--

DROP TABLE IF EXISTS `ly_draw_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_draw_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `submit_url` varchar(255) NOT NULL DEFAULT '' COMMENT '提交地址',
  `file_name` varchar(50) NOT NULL DEFAULT '' COMMENT '文件夹名称',
  `state` tinyint(4) NOT NULL DEFAULT '2' COMMENT '状态.\r\n1:打开;\r\n2:关闭;',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

--
-- Table structure for table `ly_withdrawal_channel`
--

DROP TABLE IF EXISTS `ly_withdrawal_channel`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_withdrawal_channel` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` char(30) NOT NULL DEFAULT '' COMMENT '渠道名称',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '渠道状态。\r\n1：正常；\r\n2：关闭；',
  `code` char(20) NOT NULL DEFAULT '' COMMENT '渠道编码',
  `type` char(10) NOT NULL DEFAULT 'pc' COMMENT '客户端类型：pc、app',
  `submit_url` varchar(255) NOT NULL DEFAULT '' COMMENT '代付接口地址',
  `min_amount` decimal(16,2) NOT NULL DEFAULT '50.00' COMMENT '最小代付金额',
  `max_amount` decimal(16,2) NOT NULL DEFAULT '100000.00' COMMENT '最大代付金额',
  `fee_rate` decimal(8,4) NOT NULL DEFAULT '0.0000' COMMENT '代付手续费率',
  `mode` char(20) NOT NULL DEFAULT 'traditional' COMMENT '代付模式：traditional传统代付，watchPay，jaya_pay',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `sort_type` (`sort`,`type`) USING BTREE,
  UNIQUE KEY `code` (`code`) USING BTREE,
  KEY `state` (`state`) USING BTREE,
  KEY `mode` (`mode`) USING BTREE,
  KEY `type` (`type`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='代付渠道表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_draw_config`
--

LOCK TABLES `ly_draw_config` WRITE;
/*!40000 ALTER TABLE `ly_draw_config` DISABLE KEYS */;
INSERT INTO `ly_draw_config` VALUES (2,'','',2);
/*!40000 ALTER TABLE `ly_draw_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping data for table `ly_withdrawal_channel`
--

LOCK TABLES `ly_withdrawal_channel` WRITE;
/*!40000 ALTER TABLE `ly_withdrawal_channel` DISABLE KEYS */;
INSERT INTO `ly_withdrawal_channel` VALUES
(1,'传统代付',1,1,'traditional','pc','','50.00','100000.00','0.0000','traditional',1640995200,1640995200),
(6,'JayaPay代付APP',6,1,'jayapay_app','app','','50.00','50000000.00','0.0000','jaya_pay',1640995200,1640995200),
(7,'watchPay',7,1,'watchpay','pc','','50.00','100000.00','0.0000','watchPay',1640995200,1640995200);
/*!40000 ALTER TABLE `ly_withdrawal_channel` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_homelog`
--

DROP TABLE IF EXISTS `ly_homelog`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_homelog` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户名',
  `ip` char(15) NOT NULL DEFAULT '' COMMENT '操作IP',
  `time` int(11) NOT NULL DEFAULT '0' COMMENT '操作时间',
  `params` varchar(255) NOT NULL DEFAULT '' COMMENT '参数',
  `values` mediumtext NOT NULL DEFAULT '' COMMENT '参数值',
  `func` varchar(255) NOT NULL DEFAULT '' COMMENT '方法',
  `cla` varchar(255) NOT NULL DEFAULT '' COMMENT '类',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `time` (`time`) USING BTREE,
  KEY `func` (`func`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='操作日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_homelog`
--

LOCK TABLES `ly_homelog` WRITE;
/*!40000 ALTER TABLE `ly_homelog` DISABLE KEYS */;
INSERT INTO `ly_homelog` VALUES (1,100,'**************',1734173467,'[\"lang\",\"token\"]','[\"cn\",\"2200nnH65ieniy4MJCnKpAekgMyd\\/xnwTDxrvlKT8SHVquc\"]','getuserinfo','User'),(2,100,'**************',**********,'[\"lang\",\"token\"]','[\"cn\",\"2200nnH65ieniy4MJCnKpAekgMyd\\/xnwTDxrvlKT8SHVquc\"]','getuserinfo','User'),(3,100,'**************',**********,'[\"lang\",\"token\"]','[\"cn\",\"2200nnH65ieniy4MJCnKpAekgMyd\\/xnwTDxrvlKT8SHVquc\"]','getstatisticsinfo','User'),(4,100,'**************',**********,'[\"lang\",\"token\"]','[\"cn\",\"2200nnH65ieniy4MJCnKpAekgMyd\\/xnwTDxrvlKT8SHVquc\"]','getbankcardlist','Account'),(5,100,'**************',**********,'[\"state\",\"page_no\",\"lang\",\"token\"]','[\"0\",\"1\",\"cn\",\"2200nnH65ieniy4MJCnKpAekgMyd\\/xnwTDxrvlKT8SHVquc\"]','getrechargerecord','Transaction'),(6,100,'**************',**********,'[\"type\",\"lang\",\"token\"]','[\"app\",\"cn\",\"2200nnH65ieniy4MJCnKpAekgMyd\\/xnwTDxrvlKT8SHVquc\"]','getrechargetype','Transaction'),(7,100,'**************',**********,'[\"state\",\"page_no\",\"lang\",\"token\"]','[\"0\",\"1\",\"cn\",\"2200nnH65ieniy4MJCnKpAekgMyd\\/xnwTDxrvlKT8SHVquc\"]','getrechargerecord','Transaction'),(8,100,'**************',**********,'[\"lang\",\"token\"]','[\"cn\",\"2200nnH65ieniy4MJCnKpAekgMyd\\/xnwTDxrvlKT8SHVquc\"]','getbankcardlist','Account'),(9,100,'**************',**********,'[\"state\",\"page_no\",\"lang\",\"token\"]','[\"0\",\"1\",\"cn\",\"2200nnH65ieniy4MJCnKpAekgMyd\\/xnwTDxrvlKT8SHVquc\"]','getdrawrecord','Transaction');
/*!40000 ALTER TABLE `ly_homelog` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_ip_white`
--

DROP TABLE IF EXISTS `ly_ip_white`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_ip_white` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip` char(15) NOT NULL DEFAULT '' COMMENT 'IP',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=FIXED COMMENT='IP白名单';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_ip_white`
--

LOCK TABLES `ly_ip_white` WRITE;
/*!40000 ALTER TABLE `ly_ip_white` DISABLE KEYS */;
/*!40000 ALTER TABLE `ly_ip_white` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_loginlog`
--

DROP TABLE IF EXISTS `ly_loginlog`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_loginlog` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `username` char(30) NOT NULL DEFAULT '' COMMENT '用户名',
  `ip` char(15) NOT NULL DEFAULT '' COMMENT '登录IP',
  `address` varchar(100) NOT NULL DEFAULT '' COMMENT '登录地址',
  `os` char(30) NOT NULL DEFAULT '' COMMENT '登录时的系统',
  `time` int(11) NOT NULL DEFAULT '0' COMMENT '登录时间',
  `browser` char(30) NOT NULL DEFAULT '' COMMENT '使用的额浏览器',
  `type` char(60) NOT NULL DEFAULT '' COMMENT '登入客户端',
  `isadmin` tinyint(4) NOT NULL DEFAULT '2' COMMENT '是否是登录后台。\r\n1：是；\r\n2：否；',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `uid` (`uid`) USING BTREE,
  KEY `time` (`time`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='登录日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_loginlog`
--

LOCK TABLES `ly_loginlog` WRITE;
/*!40000 ALTER TABLE `ly_loginlog` DISABLE KEYS */;
/*!40000 ALTER TABLE `ly_loginlog` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_manage`
--

DROP TABLE IF EXISTS `ly_manage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_manage` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` char(20) NOT NULL DEFAULT '' COMMENT '用户名',
  `password` char(255) NOT NULL DEFAULT '' COMMENT '密码',
  `type` int(11) NOT NULL DEFAULT '0' COMMENT '用户类型',
  `lastlogin_time` int(11) DEFAULT '0' COMMENT '上次登录时间',
  `lastlogin_ip` char(15) DEFAULT NULL COMMENT '上次登录IP',
  `state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '用户状态。\r\n1：正常；\r\n2：限制登录；',
  `safe_code` char(255) NOT NULL DEFAULT '' COMMENT '安全码',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `admins_type` (`type`) USING BTREE,
  KEY `state` (`state`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=33 DEFAULT CHARSET=utf8 ROW_FORMAT=FIXED COMMENT='后台用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_manage`
--

LOCK TABLES `ly_manage` WRITE;
/*!40000 ALTER TABLE `ly_manage` DISABLE KEYS */;
INSERT INTO `ly_manage` VALUES (24,'admin','a450/8x2i9BvYVbwhJS5RoJFHPCPCChQTs2jfPqtDlVOww',0,0,NULL,1,'8069gOE1yGX2UIutBpCIwDxwI50H+txG4OfpCU6BAWdWDQ');
/*!40000 ALTER TABLE `ly_manage` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_manage_role`
--

DROP TABLE IF EXISTS `ly_manage_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_manage_role` (
  `id` int(8) NOT NULL AUTO_INCREMENT,
  `role_name` varchar(60) NOT NULL DEFAULT '' COMMENT '权限',
  `role_url` varchar(100) NOT NULL DEFAULT '' COMMENT '权限',
  `state` tinyint(4) NOT NULL DEFAULT '1',
  `sort` tinyint(4) NOT NULL DEFAULT '1',
  `level` tinyint(4) NOT NULL DEFAULT '1',
  `cid` int(8) NOT NULL DEFAULT '0',
  `pid` int(8) NOT NULL DEFAULT '1' COMMENT '客服版状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=370 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_manage_role`
--

LOCK TABLES `ly_manage_role` WRITE;
/*!40000 ALTER TABLE `ly_manage_role` DISABLE KEYS */;

INSERT INTO `ly_manage_role` (`id`, `role_name`, `role_url`, `state`, `sort`, `level`, `cid`, `pid`) VALUES
(1, '系统设定', 'Base/index', 1, 1, 1, 0, 0),
(2, '会员管理', 'User/index', 1, 2, 1, 0, 0),
(3, '充值提现', 'Bank/index', 1, 8, 1, 0, 0),
(4, '任务管理', 'Bet/index', 1, 3, 1, 0, 0),
(5, '报表数据', 'Report/index', 1, 5, 1, 0, 0),
(10, '基本设置', 'Base/setting', 1, 1, 2, 1, 0),
(11, '权限管理', 'Base/role_list', 1, 4, 2, 1, 0),
(12, '添加权限', 'Base/role_add', 1, 0, 3, 1, 11),
(13, '修改权限', 'Base/role_edit', 1, 0, 3, 1, 11),
(14, '删除权限', 'Base/role_delete', 1, 0, 3, 1, 11),
(15, '修改设置', 'Base/setting_edit', 1, 0, 3, 1, 10),
(16, '会员中心', 'User/userlist', 1, 1, 2, 2, 0),
(17, '添加会员', 'User/add', 1, 0, 3, 2, 16),
(18, '编辑查看会员', 'User/edit', 1, 0, 3, 2, 16),
(19, '删除会员', 'User/delete', 1, 0, 3, 2, 16),
(20, '风险账号', 'User/risk', 1, 0, 3, 2, 16),
(24, '资金操作', 'User/capital', 1, 0, 3, 2, 16),
(25, '锁定', 'User/locking', 1, 0, 3, 2, 16),
(28, '管理员管理', 'Base/admins', 1, 3, 2, 1, 0),
(29, '添加管理员', 'Base/admins_add', 1, 0, 3, 1, 28),
(30, '修改管理员', 'Base/admins_edit', 1, 0, 3, 1, 28),
(31, '删除管理员', 'Base/admins_delete', 1, 0, 3, 1, 28),
(32, '设置管理员权限', 'Base/admins_set_role', 1, 0, 3, 1, 28),
(33, '站内公告', 'Base/notice', 1, 2, 2, 1, 0),
(34, '添加公告', 'Base/notice_add', 1, 0, 3, 1, 33),
(35, '修改公告', 'Base/notice_edit', 1, 0, 3, 1, 33),
(36, '删除公告', 'Base/notice_delete', 1, 0, 3, 1, 33),
(37, '后端白名单', 'Base/ip_white', 1, 5, 2, 1, 0),
(38, '添加白名单', 'Base/ip_white_add', 1, 0, 3, 1, 37),
(39, '修改白名单', 'Base/ip_white_edit', 1, 0, 3, 1, 37),
(40, '删除白名单', 'Base/ip_white_delete', 1, 0, 3, 1, 37),
(336, '清除缓存', 'Other/clear_cache_front', 1, 5, 2, 331, 1),
(57, '会员关系树', 'User/relation', 1, 3, 2, 2, 0),
(58, '会员银行', 'User/bank', 1, 4, 2, 2, 0),
(59, '代理迁移', 'User/team_move', 1, 2, 2, 2, 0),
(60, '银行配置', 'Bank/lists', 1, 0, 2, 3, 0),
(61, '添加银行', 'Bank/add', 1, 0, 3, 3, 60),
(62, '修改银行', 'bank/edit', 1, 0, 3, 3, 60),
(63, '删除银行', 'bank/delete', 1, 0, 3, 3, 60),
(64, '存取开关', 'Bank/bank_on_off', 1, 0, 3, 3, 60),
(66, '充值渠道', 'Bank/recharge_channel', 1, 0, 2, 3, 0),
(67, '添加渠道', 'Bank/recharge_channel_add', 1, 0, 3, 3, 66),
(68, '编辑渠道', 'Bank/recharge_channel_edit', 1, 0, 3, 3, 66),
(69, '删除渠道', 'Bank/recharge_channel_delete', 1, 0, 3, 3, 66),
(70, '渠道开关', 'Bank/recharge_channel_on_off', 1, 0, 3, 3, 66),
(71, '充值记录', 'Bank/recharge_record', 1, 0, 2, 3, 0),
(72, '提现记录', 'Bank/present_record', 1, 0, 2, 3, 0),
(73, '收款帐号', 'Bank/receivables', 1, 0, 2, 3, 0),
(74, '添加账号', 'Bank/receivables_add', 1, 0, 3, 3, 73),
(75, '修改账号', 'Bank/receivables_edit', 1, 0, 3, 3, 73),
(76, '删除帐号', 'Bank/receivables_delete', 1, 0, 3, 3, 73),
(77, '账号开关', 'Bank/receivables_on_off', 1, 0, 3, 3, 73),
(78, '出款设置', 'Bank/set_out_money', 1, 0, 2, 3, 0),
(84, '资金流水', 'Bet/financial', 1, 5, 2, 4, 0),
(89, '修复开奖', 'Bet/change_dan_repair_lottery', 1, 0, 3, 4, 0),
(90, '投注日志', 'Bet/log', 1, 5, 2, 4, 0),
(93, '全局统计', 'Report/counts', 1, 0, 2, 5, 0),
(94, '每日报表', 'Report/data', 1, 0, 2, 5, 0),
(362, '编辑', 'bank/paymentEdit', 1, 30, 3, 3, 78),
(98, '团队报表', 'Report/team_statistic', 1, 0, 2, 5, 0),
(167, '流水详情', 'Bet/financial_dateils', 1, 1, 3, 4, 84),
(179, '订单审核', 'Bank/rechargeDispose', 1, 1, 3, 3, 71),
(180, '详情', 'Bank/rechargeDetail', 1, 1, 3, 3, 71),
(181, '风控审核', 'Bank/controlAudit', 1, 1, 3, 3, 72),
(182, '财务审核', 'Bank/financialAudit', 1, 1, 3, 3, 72),
(183, '提现详情', 'Bank/withdrawalsDetails', 1, 1, 3, 3, 72),
(184, '出款', 'Bank/withdrawalsPayment', 1, 1, 3, 3, 72),
(213, '添加二维码', 'Bank/receivablesQrcodeAdd', 1, 1, 3, 3, 73),
(214, '二维码上传', 'Bank/qrcodeUpload', 1, 1, 3, 3, 73),
(215, '等级开关', 'Bank/openLevel', 1, 1, 3, 3, 73),
(216, '二维码编辑', 'Bank/receivablesQrcodeEdit', 1, 1, 3, 3, 73),
(218, '人工存提', 'User/artificialAction', 1, 7, 2, 2, 1),
(219, '批量处理', 'User/artificialBatch', 1, 1, 3, 2, 218),
(220, '文件上传', 'User/getExcelData', 1, 1, 3, 2, 218),
(337, '任务审核', 'Bet/taskAudit', 1, 34, 3, 4, 306),
(222, '添加分类', 'Base/groupAdd', 1, 1, 3, 1, 33),
(223, '分类列表', 'Base/groupList', 1, 1, 3, 1, 33),
(224, '修改分类', 'Base/groupEdit', 1, 1, 3, 1, 33),
(227, '分类删除', 'Base/groupDel', 1, 28, 3, 1, 33),
(237, '编辑银行', 'User/userBanEdit', 1, 24, 3, 2, 58),
(292, '私信', 'User/secret', 1, 20, 3, 2, 16),
(297, '码商', 'User/isAdmin', 1, 22, 3, 2, 16),
(306, '任务列表', 'Bet/taskList', 1, 17, 2, 4, 1),
(308, '任务订单', 'Bet/userTaskList', 1, 18, 2, 4, 1),
(310, '任务类型', 'Bet/TaskClass', 1, 4, 3, 4, 306),
(312, '添加任务', 'Bet/taskAdd', 1, 1, 3, 4, 306),
(313, '编辑任务', 'Bet/taskEdit', 1, 2, 3, 4, 306),
(314, '删除任务', 'Bet/taskDel', 1, 3, 3, 4, 306),
(315, '订单详情', 'Bet/userTaskListDetails', 1, 25, 3, 4, 308),
(316, '订单审核', 'Bet/userTaskAudit', 1, 26, 3, 4, 308),
(318, '添加任务类型', 'Bet/TaskClassAdd', 1, 5, 3, 4, 306),
(319, '删除任务类型', 'Bet/taskClassDel', 1, 6, 3, 4, 306),
(322, '是否推荐', 'Bet/projectRecommend', 1, 31, 3, 4, 306),
(323, '编辑任务类型', 'bet/TaskClassEdit', 1, 7, 3, 4, 306),
(325, '上传文件', 'Base/upload', 1, 34, 3, 1, 10),
(326, '会员等级', 'User/userLevel', 1, 23, 2, 2, 1),
(327, '添加等级', 'User/userLevelAdd', 1, 24, 3, 2, 326),
(328, '编辑等级', 'User/userLevelEdit', 1, 25, 3, 2, 326),
(331, '其他', 'Other/index', 1, 8, 1, 0, 1),
(332, '前端登录日志', 'Other/front_login_log', 1, 1, 2, 331, 1),
(333, '后台登录日志', 'Other/after_login_log', 1, 2, 2, 331, 1),
(334, '前端操作日志', 'Other/front_operation_log', 1, 3, 2, 331, 1),
(335, '后台操作日志', 'Other/after_operation_log', 1, 4, 2, 331, 1),
(339, 'Vip会员', 'User/userVip', 1, 26, 2, 2, 1),
(340, '编辑订单', 'bet/userTaskEdit', 1, 34, 3, 4, 308),
(341, '幻灯片管理', 'Base/slideList', 1, 30, 2, 1, 1),
(342, '添加幻灯片', 'Base/slideAdd', 1, 31, 3, 1, 341),
(343, '删除幻灯片', 'Base/slideDel', 1, 32, 3, 1, 341),
(344, '幻灯片状态', 'Base/slideStatus', 1, 33, 3, 1, 341),
(346, '信用评估', 'User/creditAssess', 1, 28, 3, 2, 16),
(347, '余额宝', 'Yuebao/index', 1, 8, 1, 0, 1),
(348, '产品列表', 'Yuebao/lists', 1, 1, 2, 347, 1),
(349, '添加产品', 'Yuebao/add', 1, 2, 3, 347, 348),
(350, '编辑产品', 'Yuebao/edit', 1, 3, 3, 347, 348),
(351, '删除产品', 'Yuebao/dedete', 1, 4, 3, 347, 348),
(352, '余额宝购买记录', 'yuebao/jilulist', 1, 5, 2, 347, 1),
(356, '任务模板', 'Bet/taskModelList', 1, 20, 2, 4, 1),
(355, '添加', 'bank/paymentAdd', 1, 29, 3, 3, 78),
(357, '添加模板', 'Bet/taskModelAdd', 1, 21, 3, 4, 356),
(358, '编辑模板', 'Bet/taskModelEdit', 1, 22, 3, 4, 356),
(359, '删除模板', 'Bet/taskModelDel', 1, 23, 3, 4, 356),
(363, '大转盘', 'Wheel', 1, 8, 1, 0, 1),
(361, '从模板添加任务', 'Bet/taskTplAdd', 1, 24, 3, 4, 306),
(364, '大转盘配置', 'Wheel/config', 1, 1, 2, 363, 1),
(365, '奖品列表', 'Wheel/index', 1, 2, 2, 363, 1),
(366, '编辑', 'Wheel/edit', 1, 3, 3, 363, 365),
(367, '删除', 'Wheel/delete', 1, 4, 3, 363, 365),
(368, '获奖者名单', 'Wheel/win', 1, 5, 2, 363, 1),
(369, '模板下载', 'Bet/downloadTaskTemplate', 1, 8, 3, 4, 306);
/*!40000 ALTER TABLE `ly_manage_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_manage_type`
--

DROP TABLE IF EXISTS `ly_manage_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_manage_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type_name` char(30) NOT NULL DEFAULT '' COMMENT '后台用户类型名称',
  `power` char(50) NOT NULL DEFAULT '' COMMENT '权限',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 ROW_FORMAT=FIXED COMMENT='后台用户类型表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_manage_type`
--

LOCK TABLES `ly_manage_type` WRITE;
/*!40000 ALTER TABLE `ly_manage_type` DISABLE KEYS */;
INSERT INTO `ly_manage_type` VALUES (1,'超级管理员','[1,2,3,4,5,6,7]'),(2,'财务','[1,2,3,4,5,6,7]'),(3,'风控','[1,2,3,4,5,6,7]'),(4,'客服','[1,2,3,4,5,6,7]'),(5,'事务处理','[1,2,3,4,5,6,7]');
/*!40000 ALTER TABLE `ly_manage_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_manage_user_role`
--

DROP TABLE IF EXISTS `ly_manage_user_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_manage_user_role` (
  `id` int(8) NOT NULL AUTO_INCREMENT,
  `uid` int(8) DEFAULT NULL COMMENT '管理员ID',
  `role_id` int(8) DEFAULT NULL COMMENT '权限ID',
  `state` tinyint(4) DEFAULT '1',
  `sort` tinyint(4) DEFAULT '0' COMMENT '排序',
  `level` tinyint(4) DEFAULT '0' COMMENT '级',
  `cid` int(8) DEFAULT '0' COMMENT '顶级导航',
  `pid` int(8) DEFAULT '0' COMMENT '次级导航',
  `role_name` varchar(60) DEFAULT NULL COMMENT '权限名',
  `role_url` varchar(100) DEFAULT NULL COMMENT '权限',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `id` (`id`) USING BTREE,
  UNIQUE KEY `uid_role_unique` (`uid`, `role_id`) USING BTREE COMMENT '防止同一用户重复权限',
  KEY `role_id` (`role_id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=4937 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_manage_user_role`
--

LOCK TABLES `ly_manage_user_role` WRITE;
/*!40000 ALTER TABLE `ly_manage_user_role` DISABLE KEYS */;

INSERT INTO `ly_manage_user_role` (`id`, `uid`, `role_id`, `state`, `sort`, `level`, `cid`, `pid`, `role_name`, `role_url`) VALUES
(2756, 17, 237, 1, 24, 3, 2, 58, '编辑银行', 'User/userBanEdit'),
(2754, 17, 227, 1, 28, 3, 1, 33, '分类删除', 'Base/groupDel'),
(2753, 17, 224, 1, 1, 3, 1, 33, '修改分类', 'Base/groupEdit'),
(2752, 17, 223, 1, 1, 3, 1, 33, '分类列表', 'Base/groupList'),
(2751, 17, 222, 1, 1, 3, 1, 33, '添加分类', 'Base/groupAdd'),
(2750, 17, 337, 1, 34, 3, 4, 306, '任务审核', 'Bet/taskAudit'),
(2749, 17, 220, 2, 0, 3, 2, 218, '文件上传', 'User/getExcelData'),
(2748, 17, 219, 2, 0, 3, 2, 218, '批量处理', 'User/artificialBatch'),
(2747, 17, 218, 2, 7, 2, 2, 0, '人工存提', 'User/artificialAction'),
(2746, 17, 216, 1, 0, 3, 3, 73, '二维码编辑', 'Bank/receivablesQrcodeEdit'),
(2745, 17, 215, 1, 0, 3, 3, 73, '等级开关', 'Bank/openLevel'),
(2744, 17, 214, 1, 1, 3, 3, 73, '二维码上传', 'Bank/qrcodeUpload'),
(2743, 17, 213, 1, 0, 3, 3, 73, '添加二维码', 'Bank/receivablesQrcodeAdd'),
(2740, 17, 1, 1, 1, 1, 0, 0, '系统设定', 'Base/index'),
(2739, 17, 2, 1, 2, 1, 0, 0, '会员管理', 'User/index'),
(2738, 17, 3, 1, 8, 1, 0, 0, '充值提现', 'Bank/index'),
(2737, 17, 4, 1, 3, 1, 0, 0, '任务管理', 'Bet/index'),
(2736, 17, 5, 1, 5, 1, 0, 0, '报表数据', 'Report/index'),
(2734, 17, 10, 1, 1, 2, 1, 0, '基本设置', 'Base/setting'),
(2733, 17, 11, 1, 4, 2, 1, 0, '权限管理', 'Base/role_list'),
(2732, 17, 12, 1, 0, 3, 1, 11, '添加权限', 'Base/role_add'),
(2731, 17, 13, 1, 0, 3, 1, 11, '修改权限', 'Base/role_edit'),
(2730, 17, 14, 1, 0, 3, 1, 11, '删除权限', 'Base/role_delete'),
(2729, 17, 15, 1, 0, 3, 1, 10, '修改设置', 'Base/setting_edit'),
(2728, 17, 16, 1, 1, 2, 2, 0, '会员中心', 'User/userlist'),
(2727, 17, 17, 1, 0, 3, 2, 16, '添加会员', 'User/add'),
(2726, 17, 18, 1, 0, 3, 2, 16, '编辑查看会员', 'User/edit'),
(2725, 17, 19, 1, 0, 3, 2, 16, '删除会员', 'User/delete'),
(2724, 17, 20, 1, 0, 3, 2, 16, '风险账号', 'User/risk'),
(2723, 17, 24, 1, 0, 3, 2, 16, '资金操作', 'User/capital'),
(2722, 17, 25, 1, 0, 3, 2, 16, '锁定', 'User/locking'),
(2721, 17, 28, 1, 3, 2, 1, 0, '管理员管理', 'Base/admins'),
(2720, 17, 29, 1, 0, 3, 1, 28, '添加管理员', 'Base/admins_add'),
(2719, 17, 30, 1, 0, 3, 1, 28, '修改管理员', 'Base/admins_edit'),
(2718, 17, 31, 1, 0, 3, 1, 28, '删除管理员', 'Base/admins_delete'),
(2717, 17, 32, 1, 0, 3, 1, 28, '设置管理员权限', 'Base/admins_set_role'),
(2716, 17, 33, 1, 2, 2, 1, 0, '站内公告', 'Base/notice'),
(2715, 17, 34, 1, 0, 3, 1, 33, '添加公告', 'Base/notice_add'),
(2714, 17, 35, 1, 0, 3, 1, 33, '修改公告', 'Base/notice_edit'),
(2713, 17, 36, 1, 0, 3, 1, 33, '删除公告', 'Base/notice_delete'),
(2712, 17, 37, 1, 5, 2, 1, 0, '后端白名单', 'Base/ip_white'),
(2711, 17, 38, 1, 0, 3, 1, 37, '添加白名单', 'Base/ip_white_add'),
(2710, 17, 39, 1, 0, 3, 1, 37, '修改白名单', 'Base/ip_white_edit'),
(2709, 17, 40, 1, 0, 3, 1, 37, '删除白名单', 'Base/ip_white_delete'),
(2708, 17, 341, 1, 30, 2, 1, 0, '幻灯片管理', 'Base/slideList'),
(2707, 17, 339, 1, 26, 2, 2, 0, 'Vip会员', 'User/userVip'),
(2706, 17, 336, 1, 5, 2, 331, 0, '清除缓存', 'Other/clear_cache_front'),
(2705, 17, 57, 1, 3, 2, 2, 0, '会员关系树', 'User/relation'),
(2704, 17, 58, 1, 4, 2, 2, 0, '会员银行', 'User/bank'),
(2703, 17, 59, 2, 2, 2, 2, 0, '代理迁移', 'User/team_move'),
(2702, 17, 60, 1, 0, 2, 3, 0, '银行配置', 'Bank/lists'),
(2701, 17, 61, 1, 0, 3, 3, 60, '添加银行', 'Bank/add'),
(2700, 17, 62, 1, 0, 3, 3, 60, '修改银行', 'bank/edit'),
(2699, 17, 63, 1, 0, 3, 3, 60, '删除银行', 'bank/delete'),
(2698, 17, 64, 1, 0, 3, 3, 60, '存取开关', 'Bank/bank_on_off'),
(2697, 17, 66, 1, 0, 2, 3, 0, '充值渠道', 'Bank/recharge_channel'),
(2696, 17, 67, 1, 0, 3, 3, 66, '添加渠道', 'Bank/recharge_channel_add'),
(2695, 17, 68, 1, 0, 3, 3, 66, '编辑渠道', 'Bank/recharge_channel_edit'),
(2694, 17, 69, 1, 0, 3, 3, 66, '删除渠道', 'Bank/recharge_channel_delete'),
(2693, 17, 70, 1, 0, 3, 3, 66, '渠道开关', 'Bank/recharge_channel_on_off'),
(2692, 17, 71, 1, 0, 2, 3, 0, '充值记录', 'Bank/recharge_record'),
(2691, 17, 73, 1, 0, 2, 3, 0, '收款帐号', 'Bank/receivables'),
(2690, 17, 72, 1, 0, 2, 3, 0, '提现记录', 'Bank/present_record'),
(2689, 17, 74, 1, 0, 3, 3, 73, '添加账号', 'Bank/receivables_add'),
(2688, 17, 75, 1, 0, 3, 3, 73, '修改账号', 'Bank/receivables_edit'),
(2687, 17, 76, 1, 0, 3, 3, 73, '删除帐号', 'Bank/receivables_delete'),
(2686, 17, 77, 1, 0, 3, 3, 73, '账号开关', 'Bank/receivables_on_off'),
(2685, 17, 78, 2, 0, 2, 3, 0, '出款设置', 'Bank/set_out_money'),
(2683, 17, 84, 1, 5, 2, 4, 0, '资金流水', 'Bet/financial'),
(2682, 17, 89, 1, 0, 3, 4, 0, '修复开奖', 'Bet/change_dan_repair_lottery'),
(2681, 17, 90, 2, 5, 2, 4, 0, '投注日志', 'Bet/log'),
(2680, 17, 93, 1, 0, 2, 5, 0, '全局统计', 'Report/counts'),
(2679, 17, 94, 1, 0, 2, 5, 0, '每日报表', 'Report/data'),
(2676, 17, 98, 1, 0, 2, 5, 0, '团队报表', 'Report/team_statistic'),
(2669, 17, 167, 1, 1, 3, 4, 84, '流水详情', 'Bet/financial_dateils'),
(2668, 17, 179, 1, 1, 3, 3, 71, '订单审核', 'Bank/rechargeDispose'),
(2667, 17, 180, 1, 1, 3, 3, 71, '详情', 'Bank/rechargeDetail'),
(2666, 17, 181, 1, 1, 3, 3, 72, '风控审核', 'Bank/controlAudit'),
(2665, 17, 182, 1, 1, 3, 3, 72, '财务审核', 'Bank/financialAudit'),
(2664, 17, 184, 1, 1, 3, 3, 72, '出款', 'Bank/withdrawalsPayment'),
(2663, 17, 183, 1, 1, 3, 3, 72, '提现详情', 'Bank/withdrawalsDetails'),
(3656, 24, 59, 1, 2, 2, 2, 0, '代理迁移', 'User/team_move'),
(3655, 24, 58, 1, 4, 2, 2, 0, '会员银行', 'User/bank'),
(3654, 24, 57, 1, 3, 2, 2, 0, '会员关系树', 'User/relation'),
(3653, 24, 336, 1, 5, 2, 331, 0, '清除缓存', 'Other/clear_cache_front'),
(3652, 24, 339, 1, 26, 2, 2, 0, 'Vip会员', 'User/userVip'),
(3651, 24, 341, 1, 30, 2, 1, 0, '幻灯片管理', 'Base/slideList'),
(3650, 24, 40, 1, 0, 3, 1, 37, '删除白名单', 'Base/ip_white_delete'),
(3649, 24, 39, 1, 0, 3, 1, 37, '修改白名单', 'Base/ip_white_edit'),
(3648, 24, 38, 1, 0, 3, 1, 37, '添加白名单', 'Base/ip_white_add'),
(3647, 24, 37, 1, 5, 2, 1, 0, '后端白名单', 'Base/ip_white'),
(3646, 24, 36, 1, 0, 3, 1, 33, '删除公告', 'Base/notice_delete'),
(3645, 24, 35, 1, 0, 3, 1, 33, '修改公告', 'Base/notice_edit'),
(3644, 24, 34, 1, 0, 3, 1, 33, '添加公告', 'Base/notice_add'),
(3643, 24, 33, 1, 2, 2, 1, 0, '站内公告', 'Base/notice'),
(3642, 24, 32, 1, 0, 3, 1, 28, '设置管理员权限', 'Base/admins_set_role'),
(3641, 24, 31, 1, 0, 3, 1, 28, '删除管理员', 'Base/admins_delete'),
(3640, 24, 30, 1, 0, 3, 1, 28, '修改管理员', 'Base/admins_edit'),
(3639, 24, 29, 1, 0, 3, 1, 28, '添加管理员', 'Base/admins_add'),
(3638, 24, 28, 1, 3, 2, 1, 0, '管理员管理', 'Base/admins'),
(3637, 24, 25, 1, 0, 3, 2, 16, '锁定', 'User/locking'),
(3636, 24, 24, 1, 0, 3, 2, 16, '资金操作', 'User/capital'),
(3635, 24, 20, 1, 0, 3, 2, 16, '风险账号', 'User/risk'),
(3634, 24, 19, 1, 0, 3, 2, 16, '删除会员', 'User/delete'),
(3633, 24, 18, 1, 0, 3, 2, 16, '编辑查看会员', 'User/edit'),
(3632, 24, 17, 1, 0, 3, 2, 16, '添加会员', 'User/add'),
(3631, 24, 16, 1, 1, 2, 2, 0, '会员中心', 'User/userlist'),
(3630, 24, 15, 1, 0, 3, 1, 10, '修改设置', 'Base/setting_edit'),
(3629, 24, 14, 1, 0, 3, 1, 11, '删除权限', 'Base/role_delete'),
(3628, 24, 13, 1, 0, 3, 1, 11, '修改权限', 'Base/role_edit'),
(3627, 24, 12, 1, 0, 3, 1, 11, '添加权限', 'Base/role_add'),
(3626, 24, 11, 1, 4, 2, 1, 0, '权限管理', 'Base/role_list'),
(3625, 24, 10, 1, 1, 2, 1, 0, '基本设置', 'Base/setting'),
(3623, 24, 5, 1, 5, 1, 0, 0, '报表数据', 'Report/index'),
(3622, 24, 4, 1, 3, 1, 0, 0, '任务管理', 'Bet/index'),
(3621, 24, 3, 1, 8, 1, 0, 0, '充值提现', 'Bank/index'),
(3620, 24, 2, 1, 2, 1, 0, 0, '会员管理', 'User/index'),
(3619, 24, 1, 1, 1, 1, 0, 0, '系统设定', 'Base/index'),
(3616, 24, 213, 1, 0, 3, 3, 73, '添加二维码', 'Bank/receivablesQrcodeAdd'),
(3615, 24, 214, 1, 1, 3, 3, 73, '二维码上传', 'Bank/qrcodeUpload'),
(3614, 24, 215, 1, 0, 3, 3, 73, '等级开关', 'Bank/openLevel'),
(3613, 24, 216, 1, 0, 3, 3, 73, '二维码编辑', 'Bank/receivablesQrcodeEdit'),
(3612, 24, 218, 1, 7, 2, 2, 0, '人工存提', 'User/artificialAction'),
(3611, 24, 219, 1, 0, 3, 2, 218, '批量处理', 'User/artificialBatch'),
(3610, 24, 220, 1, 0, 3, 2, 218, '文件上传', 'User/getExcelData'),
(3609, 24, 337, 1, 34, 3, 4, 306, '任务审核', 'Bet/taskAudit'),
(3608, 24, 222, 1, 1, 3, 1, 33, '添加分类', 'Base/groupAdd'),
(3607, 24, 223, 1, 1, 3, 1, 33, '分类列表', 'Base/groupList'),
(3606, 24, 224, 1, 1, 3, 1, 33, '修改分类', 'Base/groupEdit'),
(3605, 24, 227, 1, 28, 3, 1, 33, '分类删除', 'Base/groupDel'),
(3603, 24, 237, 1, 24, 3, 2, 58, '编辑银行', 'User/userBanEdit'),
(3597, 24, 335, 1, 4, 2, 331, 0, '后台操作日志', 'Other/after_operation_log'),
(3596, 24, 342, 1, 31, 3, 1, 341, '添加幻灯片', 'Base/slideAdd'),
(3595, 24, 343, 1, 32, 3, 1, 341, '删除幻灯片', 'Base/slideDel'),
(3594, 24, 344, 1, 33, 3, 1, 341, '幻灯片状态', 'Base/slideStatus'),
(3593, 24, 346, 1, 28, 3, 2, 16, '信用评估', 'User/creditAssess'),
(3592, 23, 346, 1, 28, 3, 2, 16, '信用评估', 'User/creditAssess'),
(3591, 23, 344, 1, 33, 3, 1, 341, '幻灯片状态', 'Base/slideStatus'),
(3590, 23, 343, 1, 32, 3, 1, 341, '删除幻灯片', 'Base/slideDel'),
(3589, 23, 342, 1, 31, 3, 1, 341, '添加幻灯片', 'Base/slideAdd'),
(3588, 23, 335, 1, 4, 2, 331, 0, '后台操作日志', 'Other/after_operation_log'),
(2767, 17, 292, 1, 20, 3, 2, 16, '私信', 'User/secret'),
(2768, 17, 297, 1, 22, 3, 2, 16, '码商', 'User/isAdmin'),
(2769, 17, 323, 1, 7, 3, 4, 306, '编辑任务类型', 'bet/TaskClassEdit'),
(2770, 17, 322, 1, 31, 3, 4, 306, '是否推荐', 'Bet/projectRecommend'),
(2771, 17, 319, 1, 6, 3, 4, 306, '删除任务类型', 'Bet/taskClassDel'),
(2772, 17, 318, 1, 5, 3, 4, 306, '添加任务类型', 'Bet/TaskClassAdd'),
(2773, 17, 316, 1, 26, 3, 4, 308, '订单审核', 'Bet/userTaskAudit'),
(2774, 17, 327, 1, 24, 3, 2, 326, '添加等级', 'User/userLevelAdd'),
(2775, 17, 315, 1, 25, 3, 4, 308, '订单详情', 'Bet/userTaskListDetails'),
(2776, 17, 314, 1, 3, 3, 4, 306, '删除任务', 'Bet/taskDel'),
(2777, 17, 313, 1, 2, 3, 4, 306, '编辑任务', 'Bet/taskEdit'),
(2778, 17, 312, 1, 1, 3, 4, 306, '添加任务', 'Bet/taskAdd'),
(2779, 17, 328, 1, 25, 3, 2, 326, '编辑等级', 'User/userLevelEdit'),
(2780, 17, 310, 1, 4, 3, 4, 306, '任务类型', 'Bet/TaskClass'),
(2781, 17, 340, 1, 34, 3, 4, 308, '编辑订单', 'bet/userTaskEdit'),
(2782, 17, 308, 1, 18, 2, 4, 0, '任务订单', 'Bet/userTaskList'),
(2783, 17, 326, 1, 23, 2, 2, 0, '会员等级', 'User/userLevel'),
(2784, 17, 325, 1, 34, 3, 1, 10, '上传文件', 'Base/upload'),
(2785, 17, 306, 1, 17, 2, 4, 0, '任务列表', 'Bet/taskList'),
(2786, 17, 331, 1, 8, 1, 0, 0, '其他', 'Other/index'),
(2787, 17, 332, 1, 1, 2, 331, 0, '前端登录日志', 'Other/front_login_log'),
(2788, 17, 333, 1, 2, 2, 331, 0, '后台登录日志', 'Other/after_login_log'),
(2789, 17, 334, 1, 3, 2, 331, 0, '前端操作日志', 'Other/front_operation_log'),
(2790, 17, 335, 1, 4, 2, 331, 0, '后台操作日志', 'Other/after_operation_log'),
(2791, 17, 342, 1, 31, 3, 1, 341, '添加幻灯片', 'Base/slideAdd'),
(2792, 17, 343, 1, 32, 3, 1, 341, '删除幻灯片', 'Base/slideDel'),
(2793, 17, 344, 1, 33, 3, 1, 341, '幻灯片状态', 'Base/slideStatus'),
(2794, 17, 346, 1, 28, 3, 2, 16, '信用评估', 'User/creditAssess'),
(3554, 23, 237, 1, 24, 3, 2, 58, '编辑银行', 'User/userBanEdit'),
(3552, 23, 227, 1, 28, 3, 1, 33, '分类删除', 'Base/groupDel'),
(3551, 23, 224, 1, 1, 3, 1, 33, '修改分类', 'Base/groupEdit'),
(3550, 23, 223, 1, 1, 3, 1, 33, '分类列表', 'Base/groupList'),
(3549, 23, 222, 1, 1, 3, 1, 33, '添加分类', 'Base/groupAdd'),
(3548, 23, 337, 1, 34, 3, 4, 306, '任务审核', 'Bet/taskAudit'),
(3547, 23, 220, 2, 0, 3, 2, 218, '文件上传', 'User/getExcelData'),
(3546, 23, 219, 2, 0, 3, 2, 218, '批量处理', 'User/artificialBatch'),
(3545, 23, 218, 2, 7, 2, 2, 0, '人工存提', 'User/artificialAction'),
(3544, 23, 216, 1, 0, 3, 3, 73, '二维码编辑', 'Bank/receivablesQrcodeEdit'),
(3543, 23, 215, 1, 0, 3, 3, 73, '等级开关', 'Bank/openLevel'),
(3542, 23, 214, 1, 1, 3, 3, 73, '二维码上传', 'Bank/qrcodeUpload'),
(3541, 23, 213, 1, 0, 3, 3, 73, '添加二维码', 'Bank/receivablesQrcodeAdd'),
(3538, 23, 1, 1, 1, 1, 0, 0, '系统设定', 'Base/index'),
(3537, 23, 2, 1, 2, 1, 0, 0, '会员管理', 'User/index'),
(3536, 23, 3, 1, 8, 1, 0, 0, '充值提现', 'Bank/index'),
(3535, 23, 4, 1, 3, 1, 0, 0, '任务管理', 'Bet/index'),
(3534, 23, 5, 1, 5, 1, 0, 0, '报表数据', 'Report/index'),
(3532, 23, 10, 1, 1, 2, 1, 0, '基本设置', 'Base/setting'),
(3531, 23, 11, 1, 4, 2, 1, 0, '权限管理', 'Base/role_list'),
(3530, 23, 12, 1, 0, 3, 1, 11, '添加权限', 'Base/role_add'),
(3529, 23, 13, 1, 0, 3, 1, 11, '修改权限', 'Base/role_edit'),
(3528, 23, 14, 1, 0, 3, 1, 11, '删除权限', 'Base/role_delete'),
(3527, 23, 15, 1, 0, 3, 1, 10, '修改设置', 'Base/setting_edit'),
(3526, 23, 16, 1, 1, 2, 2, 0, '会员中心', 'User/userlist'),
(3525, 23, 17, 1, 0, 3, 2, 16, '添加会员', 'User/add'),
(3524, 23, 18, 1, 0, 3, 2, 16, '编辑查看会员', 'User/edit'),
(3523, 23, 19, 1, 0, 3, 2, 16, '删除会员', 'User/delete'),
(3522, 23, 20, 1, 0, 3, 2, 16, '风险账号', 'User/risk'),
(3521, 23, 24, 1, 0, 3, 2, 16, '资金操作', 'User/capital'),
(3520, 23, 25, 1, 0, 3, 2, 16, '锁定', 'User/locking'),
(3519, 23, 28, 1, 3, 2, 1, 0, '管理员管理', 'Base/admins'),
(3518, 23, 29, 1, 0, 3, 1, 28, '添加管理员', 'Base/admins_add'),
(3517, 23, 30, 1, 0, 3, 1, 28, '修改管理员', 'Base/admins_edit'),
(3516, 23, 31, 1, 0, 3, 1, 28, '删除管理员', 'Base/admins_delete'),
(3515, 23, 32, 1, 0, 3, 1, 28, '设置管理员权限', 'Base/admins_set_role'),
(3514, 23, 33, 1, 2, 2, 1, 0, '站内公告', 'Base/notice'),
(3513, 23, 34, 1, 0, 3, 1, 33, '添加公告', 'Base/notice_add'),
(3512, 23, 35, 1, 0, 3, 1, 33, '修改公告', 'Base/notice_edit'),
(3511, 23, 36, 1, 0, 3, 1, 33, '删除公告', 'Base/notice_delete'),
(3510, 23, 37, 1, 5, 2, 1, 0, '后端白名单', 'Base/ip_white'),
(3509, 23, 38, 1, 0, 3, 1, 37, '添加白名单', 'Base/ip_white_add'),
(3508, 23, 39, 1, 0, 3, 1, 37, '修改白名单', 'Base/ip_white_edit'),
(3507, 23, 40, 1, 0, 3, 1, 37, '删除白名单', 'Base/ip_white_delete'),
(3506, 23, 341, 1, 30, 2, 1, 0, '幻灯片管理', 'Base/slideList'),
(3505, 23, 339, 1, 26, 2, 2, 0, 'Vip会员', 'User/userVip'),
(3504, 23, 336, 1, 5, 2, 331, 0, '清除缓存', 'Other/clear_cache_front'),
(3503, 23, 57, 1, 3, 2, 2, 0, '会员关系树', 'User/relation'),
(3502, 23, 58, 1, 4, 2, 2, 0, '会员银行', 'User/bank'),
(3501, 23, 59, 1, 2, 2, 2, 0, '代理迁移', 'User/team_move'),
(3500, 23, 60, 1, 0, 2, 3, 0, '银行配置', 'Bank/lists'),
(3499, 23, 61, 1, 0, 3, 3, 60, '添加银行', 'Bank/add'),
(3498, 23, 62, 1, 0, 3, 3, 60, '修改银行', 'bank/edit'),
(3497, 23, 63, 1, 0, 3, 3, 60, '删除银行', 'bank/delete'),
(3496, 23, 64, 1, 0, 3, 3, 60, '存取开关', 'Bank/bank_on_off'),
(3495, 23, 66, 1, 0, 2, 3, 0, '充值渠道', 'Bank/recharge_channel'),
(3494, 23, 67, 1, 0, 3, 3, 66, '添加渠道', 'Bank/recharge_channel_add'),
(3493, 23, 68, 1, 0, 3, 3, 66, '编辑渠道', 'Bank/recharge_channel_edit'),
(3492, 23, 69, 1, 0, 3, 3, 66, '删除渠道', 'Bank/recharge_channel_delete'),
(3491, 23, 70, 1, 0, 3, 3, 66, '渠道开关', 'Bank/recharge_channel_on_off'),
(3490, 23, 71, 1, 0, 2, 3, 0, '充值记录', 'Bank/recharge_record'),
(3489, 23, 73, 1, 0, 2, 3, 0, '收款帐号', 'Bank/receivables'),
(3488, 23, 72, 1, 0, 2, 3, 0, '提现记录', 'Bank/present_record'),
(3487, 23, 74, 1, 0, 3, 3, 73, '添加账号', 'Bank/receivables_add'),
(3486, 23, 75, 1, 0, 3, 3, 73, '修改账号', 'Bank/receivables_edit'),
(3485, 23, 76, 1, 0, 3, 3, 73, '删除帐号', 'Bank/receivables_delete'),
(3484, 23, 77, 1, 0, 3, 3, 73, '账号开关', 'Bank/receivables_on_off'),
(3483, 23, 78, 2, 0, 2, 3, 0, '出款设置', 'Bank/set_out_money'),
(3481, 23, 84, 1, 5, 2, 4, 0, '资金流水', 'Bet/financial'),
(3480, 23, 89, 1, 0, 3, 4, 0, '修复开奖', 'Bet/change_dan_repair_lottery'),
(3479, 23, 90, 2, 5, 2, 4, 0, '投注日志', 'Bet/log'),
(3478, 23, 93, 1, 0, 2, 5, 0, '全局统计', 'Report/counts'),
(3477, 23, 94, 1, 0, 2, 5, 0, '每日报表', 'Report/data'),
(3474, 23, 98, 1, 0, 2, 5, 0, '团队报表', 'Report/team_statistic'),
(3467, 23, 167, 1, 1, 3, 4, 84, '流水详情', 'Bet/financial_dateils'),
(3466, 23, 179, 1, 1, 3, 3, 71, '订单审核', 'Bank/rechargeDispose'),
(3465, 23, 180, 1, 1, 3, 3, 71, '详情', 'Bank/rechargeDetail'),
(3464, 23, 181, 1, 1, 3, 3, 72, '风控审核', 'Bank/controlAudit'),
(3463, 23, 182, 1, 1, 3, 3, 72, '财务审核', 'Bank/financialAudit'),
(3462, 23, 184, 1, 1, 3, 3, 72, '出款', 'Bank/withdrawalsPayment'),
(3461, 23, 183, 1, 1, 3, 3, 72, '提现详情', 'Bank/withdrawalsDetails'),
(3587, 23, 334, 1, 3, 2, 331, 0, '前端操作日志', 'Other/front_operation_log'),
(3586, 23, 333, 1, 2, 2, 331, 0, '后台登录日志', 'Other/after_login_log'),
(3585, 23, 332, 1, 1, 2, 331, 0, '前端登录日志', 'Other/front_login_log'),
(3584, 23, 331, 1, 8, 1, 0, 0, '其他', 'Other/index'),
(3583, 23, 306, 1, 17, 2, 4, 0, '任务列表', 'Bet/taskList'),
(3582, 23, 325, 1, 34, 3, 1, 10, '上传文件', 'Base/upload'),
(3581, 23, 326, 1, 23, 2, 2, 0, '会员等级', 'User/userLevel'),
(3580, 23, 308, 1, 18, 2, 4, 0, '任务订单', 'Bet/userTaskList'),
(3579, 23, 340, 1, 34, 3, 4, 308, '编辑订单', 'bet/userTaskEdit'),
(3578, 23, 310, 1, 4, 3, 4, 306, '任务类型', 'Bet/TaskClass'),
(3577, 23, 328, 1, 25, 3, 2, 326, '编辑等级', 'User/userLevelEdit'),
(3576, 23, 312, 1, 1, 3, 4, 306, '添加任务', 'Bet/taskAdd'),
(3575, 23, 313, 1, 2, 3, 4, 306, '编辑任务', 'Bet/taskEdit'),
(3574, 23, 314, 1, 3, 3, 4, 306, '删除任务', 'Bet/taskDel'),
(3573, 23, 315, 1, 25, 3, 4, 308, '订单详情', 'Bet/userTaskListDetails'),
(3572, 23, 327, 1, 24, 3, 2, 326, '添加等级', 'User/userLevelAdd'),
(3571, 23, 316, 1, 26, 3, 4, 308, '订单审核', 'Bet/userTaskAudit'),
(3570, 23, 318, 1, 5, 3, 4, 306, '添加任务类型', 'Bet/TaskClassAdd'),
(3569, 23, 319, 1, 6, 3, 4, 306, '删除任务类型', 'Bet/taskClassDel'),
(3568, 23, 322, 1, 31, 3, 4, 306, '是否推荐', 'Bet/projectRecommend'),
(3567, 23, 323, 1, 7, 3, 4, 306, '编辑任务类型', 'bet/TaskClassEdit'),
(3566, 23, 297, 1, 22, 3, 2, 16, '码商', 'User/isAdmin'),
(3565, 23, 292, 1, 20, 3, 2, 16, '私信', 'User/secret'),
(3720, 24, 292, 1, 20, 3, 2, 16, '私信', 'User/secret'),
(3719, 24, 297, 1, 22, 3, 2, 16, '码商', 'User/isAdmin'),
(3718, 24, 323, 1, 7, 3, 4, 306, '编辑任务类型', 'bet/TaskClassEdit'),
(3717, 24, 322, 1, 31, 3, 4, 306, '是否推荐', 'Bet/projectRecommend'),
(3716, 24, 319, 1, 6, 3, 4, 306, '删除任务类型', 'Bet/taskClassDel'),
(3715, 24, 318, 1, 5, 3, 4, 306, '添加任务类型', 'Bet/TaskClassAdd'),
(3714, 24, 316, 1, 26, 3, 4, 308, '订单审核', 'Bet/userTaskAudit'),
(3713, 24, 327, 1, 24, 3, 2, 326, '添加等级', 'User/userLevelAdd'),
(3712, 24, 315, 1, 25, 3, 4, 308, '订单详情', 'Bet/userTaskListDetails'),
(3711, 24, 314, 1, 3, 3, 4, 306, '删除任务', 'Bet/taskDel'),
(3710, 24, 313, 1, 2, 3, 4, 306, '编辑任务', 'Bet/taskEdit'),
(3709, 24, 312, 1, 1, 3, 4, 306, '添加任务', 'Bet/taskAdd'),
(3708, 24, 328, 1, 25, 3, 2, 326, '编辑等级', 'User/userLevelEdit'),
(3707, 24, 310, 1, 4, 3, 4, 306, '任务类型', 'Bet/TaskClass'),
(3706, 24, 340, 1, 34, 3, 4, 308, '编辑订单', 'bet/userTaskEdit'),
(3705, 24, 308, 1, 18, 2, 4, 0, '任务订单', 'Bet/userTaskList'),
(3704, 24, 326, 1, 23, 2, 2, 0, '会员等级', 'User/userLevel'),
(3703, 24, 325, 1, 34, 3, 1, 10, '上传文件', 'Base/upload'),
(3702, 24, 306, 1, 17, 2, 4, 0, '任务列表', 'Bet/taskList'),
(3701, 24, 331, 1, 8, 1, 0, 0, '其他', 'Other/index'),
(3700, 24, 332, 1, 1, 2, 331, 0, '前端登录日志', 'Other/front_login_log'),
(3699, 24, 333, 1, 2, 2, 331, 0, '后台登录日志', 'Other/after_login_log'),
(3698, 24, 334, 1, 3, 2, 331, 0, '前端操作日志', 'Other/front_operation_log'),
(3697, 24, 183, 1, 1, 3, 3, 72, '提现详情', 'Bank/withdrawalsDetails'),
(3695, 24, 184, 1, 1, 3, 3, 72, '出款', 'Bank/withdrawalsPayment'),
(3694, 24, 182, 1, 1, 3, 3, 72, '财务审核', 'Bank/financialAudit'),
(3693, 24, 181, 1, 1, 3, 3, 72, '风控审核', 'Bank/controlAudit'),
(3692, 24, 180, 1, 1, 3, 3, 71, '详情', 'Bank/rechargeDetail'),
(3691, 24, 179, 1, 1, 3, 3, 71, '订单审核', 'Bank/rechargeDispose'),
(3690, 24, 167, 1, 1, 3, 4, 84, '流水详情', 'Bet/financial_dateils'),
(3683, 24, 98, 1, 0, 2, 5, 0, '团队报表', 'Report/team_statistic'),
(3680, 24, 94, 1, 0, 2, 5, 0, '每日报表', 'Report/data'),
(3679, 24, 93, 1, 0, 2, 5, 0, '全局统计', 'Report/counts'),
(3678, 24, 90, 2, 5, 2, 4, 0, '投注日志', 'Bet/log'),
(3677, 24, 89, 1, 0, 3, 4, 0, '修复开奖', 'Bet/change_dan_repair_lottery'),
(3676, 24, 84, 1, 5, 2, 4, 0, '资金流水', 'Bet/financial'),
(3674, 24, 78, 1, 0, 2, 3, 0, '出款设置', 'Bank/set_out_money'),
(3673, 24, 77, 1, 0, 3, 3, 73, '账号开关', 'Bank/receivables_on_off'),
(3672, 24, 76, 1, 0, 3, 3, 73, '删除帐号', 'Bank/receivables_delete'),
(3671, 24, 75, 1, 0, 3, 3, 73, '修改账号', 'Bank/receivables_edit'),
(3670, 24, 74, 1, 0, 3, 3, 73, '添加账号', 'Bank/receivables_add'),
(3669, 24, 72, 1, 0, 2, 3, 0, '提现记录', 'Bank/present_record'),
(3668, 24, 73, 1, 0, 2, 3, 0, '收款帐号', 'Bank/receivables'),
(3667, 24, 71, 1, 0, 2, 3, 0, '充值记录', 'Bank/recharge_record'),
(3666, 24, 70, 1, 0, 3, 3, 66, '渠道开关', 'Bank/recharge_channel_on_off'),
(3665, 24, 69, 1, 0, 3, 3, 66, '删除渠道', 'Bank/recharge_channel_delete'),
(3664, 24, 68, 1, 0, 3, 3, 66, '编辑渠道', 'Bank/recharge_channel_edit'),
(3663, 24, 67, 1, 0, 3, 3, 66, '添加渠道', 'Bank/recharge_channel_add'),
(3657, 24, 60, 1, 0, 2, 3, 0, '银行配置', 'Bank/lists'),
(3658, 24, 61, 1, 0, 3, 3, 60, '添加银行', 'Bank/add'),
(3659, 24, 62, 1, 0, 3, 3, 60, '修改银行', 'bank/edit'),
(3660, 24, 63, 1, 0, 3, 3, 60, '删除银行', 'bank/delete'),
(3661, 24, 64, 1, 0, 3, 3, 60, '存取开关', 'Bank/bank_on_off'),
(3662, 24, 66, 1, 0, 2, 3, 0, '充值渠道', 'Bank/recharge_channel'),
(3726, 24, 347, 1, 8, 1, 0, 0, '余额宝', 'Yuebao/index'),
(3727, 24, 348, 1, 1, 2, 347, 0, '产品列表', 'Yuebao/lists'),
(3728, 24, 349, 1, 2, 3, 347, 348, '添加产品', 'Yuebao/add'),
(3729, 24, 350, 1, 3, 3, 347, 348, '编辑产品', 'Yuebao/edit'),
(3730, 24, 351, 1, 4, 3, 347, 348, '删除产品', 'Yuebao/dedete'),
(3731, 24, 352, 1, 5, 2, 347, 0, '余额宝购买记录', 'yuebao/jilulist'),
(4304, 24, 359, 1, 23, 3, 4, 356, '删除模板', 'Bet/taskModelDel'),
(4302, 24, 358, 1, 22, 3, 4, 356, '编辑模板', 'Bet/taskModelEdit'),
(4297, 24, 356, 1, 20, 2, 4, 0, '任务模板', 'Bet/taskModelList'),
(4294, 24, 355, 1, 29, 3, 3, 78, '添加', 'bank/paymentAdd'),
(4300, 24, 357, 1, 21, 3, 4, 356, '添加模板', 'Bet/taskModelAdd'),
(4898, 24, 362, 1, 30, 3, 3, 78, '编辑', 'bank/paymentEdit'),
(4902, 24, 363, 1, 8, 1, 0, 0, '大转盘', 'Wheel'),
(4746, 24, 361, 1, 24, 3, 4, 306, '从模板添加任务', 'Bet/taskTplAdd'),
(4906, 24, 364, 1, 1, 2, 363, 0, '大转盘配置', 'Wheel/config'),
(4910, 24, 365, 1, 2, 2, 363, 0, '奖品列表', 'Wheel/index'),
(4914, 24, 366, 1, 3, 3, 363, 365, '编辑', 'Wheel/edit'),
(4918, 24, 367, 1, 4, 3, 363, 365, '删除', 'Wheel/delete'),
(4922, 24, 368, 1, 5, 2, 363, 0, '获奖者名单', 'Wheel/win'),
(4928, 24, 369, 1, 8, 3, 4, 306, '模板下载', 'Bet/downloadTaskTemplate'),
(4929, 24, 79, 1, 0, 2, 3, 0, '代付渠道', 'WithdrawalChannel/index'),
(4930, 24, 80, 1, 1, 3, 3, 79, '查看', 'WithdrawalChannel/index'),
(4931, 24, 81, 1, 2, 3, 3, 79, '添加', 'WithdrawalChannel/add'),
(4932, 24, 82, 1, 3, 3, 3, 79, '编辑', 'WithdrawalChannel/edit'),
(4933, 24, 83, 1, 4, 3, 3, 79, '删除', 'WithdrawalChannel/del'),
(4934, 24, 87, 1, 5, 3, 3, 79, '状态切换', 'WithdrawalChannel/state'),
(4935, 24, 85, 1, 6, 3, 3, 79, '测试连接', 'WithdrawalChannel/testConnection'),
(4936, 24, 86, 1, 7, 3, 3, 79, '获取渠道信息', 'WithdrawalChannel/getChannelInfo');
/*!40000 ALTER TABLE `ly_manage_user_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- 确保admin用户(ID=24)拥有提现渠道的完整权限
-- 如果权限已存在则忽略，如果不存在则添加
--

-- 注意：代付渠道权限已在上面的批量INSERT语句中添加，无需重复添加
-- 权限记录包括：
-- (4929,24,79,1,0,2,3,0,'代付渠道','WithdrawalChannel/index') - 主菜单
-- (4930,24,80,1,1,3,3,79,'查看','WithdrawalChannel/index') - 查看权限
-- (4931,24,81,1,2,3,3,79,'添加','WithdrawalChannel/add') - 添加权限
-- (4932,24,82,1,3,3,3,79,'编辑','WithdrawalChannel/edit') - 编辑权限
-- (4933,24,83,1,4,3,3,79,'删除','WithdrawalChannel/del') - 删除权限
-- (4934,24,85,1,6,3,3,79,'测试连接','WithdrawalChannel/testConnection') - 测试连接权限
-- (4935,24,86,1,7,3,3,79,'获取渠道信息','WithdrawalChannel/getChannelInfo') - 获取信息权限
-- (4936,24,87,1,5,3,3,79,'状态切换','WithdrawalChannel/state') - 状态切换权限
--

--
-- Table structure for table `ly_message`
--

DROP TABLE IF EXISTS `ly_message`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_message` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL DEFAULT '' COMMENT '公告标题',
  `content` text NOT NULL DEFAULT '' COMMENT '公告内容',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '公告发布时间',
  `state` tinyint(4) NOT NULL DEFAULT '2' COMMENT '状态1已;2未读',
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=90 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='公告表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_message`
--

LOCK TABLES `ly_message` WRITE;
/*!40000 ALTER TABLE `ly_message` DISABLE KEYS */;
INSERT INTO `ly_message` VALUES (28,'关于我们','<p style=\"white-space: normal; box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\">一起发成立于2014年，长期秉持＂客户第一、用户至上＂的服务宗旨，为广大用户提供最优质的购彩体验。</p><p style=\"white-space: normal; box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\">携手大发云提供最专业的彩票投注系统，为广大用户提供＂安全、可靠、极致＂的服务体验。</p><p style=\"white-space: normal; box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\">提供最合理的赔率，多渠道的收付款方式，多元化的投注玩法让广大用户享受高品质的购彩体验。</p><p style=\"white-space: normal; box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><br/></p><p style=\"white-space: normal; box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"box-sizing: inherit; font-weight: bolder; font-size: 12px;\"><span style=\"box-sizing: inherit; color: rgb(51, 51, 51);\">诚信为本：</span></span><br/>作为专业的彩票投注平台，我们承诺，为每一位用户提供最安全、最公平的购彩服务。</p><p style=\"white-space: normal; box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><br/></p><p style=\"white-space: normal; box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"box-sizing: inherit; font-weight: bolder; font-size: 12px;\"><span style=\"box-sizing: inherit; color: rgb(51, 51, 51);\">安全与信誉:</span></span></p><ul class=\" list-paddingleft-2\" style=\"list-style-type: none;\"><li><p><span style=\"box-sizing: inherit;\">专业严谨的风险控管，以及第三方资金保险，100％保证玩家资金。</span></p></li><li><p><span style=\"box-sizing: inherit;\">获得国际第三方TST公司的系统认证。</span></p></li><li><p><span style=\"box-sizing: inherit;\">诺顿分级评级为安全网站，绝无任何恶意软件。</span></p></li><li><p><span style=\"box-sizing: inherit;\">获得GEOTRUST国际认证，确保网站公平公正性，所有会员数据均经过加密，保障玩家隐私。</span></p></li><li><p><br/></p></li></ul>',1530028206,1,22),(29,'联系我们','<p style=\"box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"box-sizing: inherit; font-size: 14px;\">一起发暂时仅提供在线咨询服务，点击网站右上角＂在线客服＂即可进行在线咨询；</span></p><p style=\"box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"box-sizing: inherit; font-size: 14px;\">我们提供全天24小时在线咨询服务，全年无休；专业的客服团队，24小时为您解答所有疑问；</span></p><p><span style=\"font-size: 14px;\"></span><br/></p>',1530028254,1,22),(30,'商务合作','<p style=\"white-space: normal; box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"color: rgb(51, 51, 51); box-sizing: inherit; font-weight: bolder; font-size: 14px;\">内容合作</span></p><p style=\"white-space: normal; box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"font-size: 14px;\">如果您拥有精彩或原创的与彩票玩法彩票技巧等相关的内容或其他资源,欢迎您与我们取得联系！</span></p><p style=\"white-space: normal; box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><br/></p><p style=\"white-space: normal; box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"font-weight: bolder; font-size: 14px; box-sizing: inherit; color: rgb(51, 51, 51);\">广告合作<br/></span></p><p style=\"white-space: normal; box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"font-size: 14px;\">通过对网站广告位的互换及其他自由组合形式的广告资源置换来扩大宣传、增加多样化服务入口，最终以提升网站用户能获取更多附加价值为目的。如果您有相应的资源，非常欢迎您和我们取得联系。</span></p><p style=\"white-space: normal; box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><br/></p><p style=\"white-space: normal; box-sizing: inherit; font-size: 14px; overflow-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"font-size: 14px;\"><span style=\"font-weight: bolder; font-size: 14px; box-sizing: inherit; color: rgb(51, 51, 51);\">媒体合作</span><br/>如果您拥有互联网、微信、微博等各类传统与新媒体的丰富资源欢迎您与我们取得联系,让我们通过双方的友好合作来共同提高彼此的影响力！</span></p>',1530028288,1,22),(31,'法律声明','<p><span style=\"font-size: 14px;\">本网站提供的任何内容（包括但不限于数据、文字、图表、图象、声音或录象等）的版权均属于本网站或相关权利人。未经本网站或相关权利人事先的书面许可，您不得以任何方式擅自复制、再造、传播、出版、转帖、改编或陈列本网站的内容。同时，未经本网站书面许可，对于本网站上的任何内容，任何人不得在非本网站所属的服务器上做镜像。任何未经授权使用本网站的行为都将违反《中华人民共和国著作权法》和其他法律法规以及有关国际公约的规定。</span></p>',1530028319,1,22),(32,'隐私声明','<p style=\"white-space: normal; box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"box-sizing: inherit; font-size: 14px;\">在登陆时向我们提供您的个人信息是基于对我们的信任，我们会以专业、谨慎和负责的态度对待您的个人信息。我们认为隐私权是您的重要权利，我们尊重您的隐私权，您提供的信息只能用于帮助我们为您提供更好的服务，因此，我们制定了个人信息保密制度以保护您的个人信息。凡以任何方式登陆本网站或直接、间接使用本网站资料者，视为自愿接受本网站声明的约束。我们的隐私权保护条款如下：</span></p><p style=\"white-space: normal; box-sizing: inherit; font-size: 14px; overflow-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"font-size: 14px;\"><span style=\"color: rgb(51, 51, 51); font-size: 14px; box-sizing: inherit; font-weight: bolder;\">个人信息的收集</span><br/>在您注册、使用本网站服务时，经您的同意，我们收集与个人身份有关的信息。如果您无法提供相应信息，可能会不能使用对应服务。我们也会基于优化用户体验的目的，收集其他有关的信息，以便优化我们的网站服务。<br/><span style=\"font-weight: bolder; font-size: 14px; box-sizing: inherit; color: rgb(51, 51, 51);\">隐私的保护</span><br/>保护用户隐私是本网站的一项基本政策。本网站不会公布或传播您在本网站注册的任何资料，但下列情况除外：<br/>1）事先获得用户的明确授权；<br/>2）用户对自身信息保密不当原因，导致用户非公开信息泄露；<br/>3）由于网络线路、黑客攻击、计算机病毒、政府管制等原因造成的资料泄露、丢失、被盗用或被篡改等；<br/>4）根据有关法律法规的要求；&nbsp;<br/>5）依据法院或仲裁机构的裁判或裁决，以及其他司法程序的要求；<br/>6）按照相关政府主管部门的要求；<br/><span style=\"color: rgb(51, 51, 51); font-size: 14px; box-sizing: inherit; font-weight: bolder;\">自我更新与信息公开</span><br/>我们鼓励您自我更新和修改个人信息以使其安全和有效。您能在任何时候非常容易地获取并修改您的个人信息，您可以随时自行决定修改、删除您在网站上的相关资料。您是唯一对您的账号和密码信息负有保密责任的人，任何情况下，请小心妥善保管。<br/>无论何时您自愿在公开场合披露个人信息， 此种信息可能被他人收集及使用，因此造成您的个人信息泄露，网站不承担责任。<br/><span style=\"color: rgb(51, 51, 51); font-size: 14px; box-sizing: inherit; font-weight: bolder;\">提示</span><br/>我们可能会不时修改我们的隐私政策，这些修改会反映在本声明中，我们的任何修改都会将您的权益和满意度置於首位，我们希望您在每次访问我们的网页时都查阅我们的隐私声明，用户继续享用服务，则视为接受服务条款的变动。</span></p>',1530028353,1,22),(33,'什么是可提现金额','<p style=\"box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"box-sizing: inherit; font-size: 14px;\">可提现金额，就是账户可以用来提现的金额，</span></p><p style=\"box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"box-sizing: inherit; font-size: 14px;\">为防止洗钱，充值的钱，要进行投注，才可以提现，</span></p><p style=\"box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><br/></p><p style=\"box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"font-size: 14px;\"><span style=\"font-weight: bolder; font-size: 14px; box-sizing: inherit; color: rgb(51, 51, 51);\">可提现金额计算公式如下：</span>&nbsp;&nbsp;</span></p><ul style=\"list-style-type: none;\" class=\" list-paddingleft-2\"><li><p style=\"box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"box-sizing: inherit; font-family: arial, helvetica, sans-serif; color: rgb(51, 51, 51); background-color: rgb(255, 255, 255); font-size: 14px;\">可提现金额=投注金额+中奖金额（小于或等于余额）</span></p></li></ul><p style=\"box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"box-sizing: inherit; font-family: arial, helvetica, sans-serif; color: rgb(51, 51, 51); background-color: rgb(255, 255, 255); font-size: 14px;\"></span></p><p style=\"box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"box-sizing: inherit; font-family: arial, helvetica, sans-serif; color: rgb(51, 51, 51); background-color: rgb(255, 255, 255); font-size: 14px;\">*投注金额不含撤单的投注金额</span></p><p><br/></p>',1530028410,1,22),(34,'账户安全','<p style=\"box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"box-sizing: inherit; font-size: 14px;\">为了广大用户的账户安全，网站的＂安全中心＂提供了多项提升账户安全系数的功能；</span></p><p style=\"box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"font-size: 14px;\">新用户注册完成后，请及时进入＂安全中心＂绑定＂密保手机＂<span style=\"box-sizing: inherit; font-size: 12px;\">＂</span>密保问题＂＂密保邮箱＂；</span></p><p style=\"box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"font-size: 14px;\">用户忘记密码时，可通过以上功能，找回密码；</span></p><p><br/></p>',1530028432,1,22),(35,'如何绑定银行卡','<p style=\"box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"box-sizing: inherit; font-size: 14px;\">进入＂我的账户＂＂银行卡管理＂可以绑定您要用来提现的银行卡；</span></p><p style=\"box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"box-sizing: inherit; font-size: 14px;\">绑定银行卡前，需先设置安全密码；</span></p><p style=\"box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"font-size: 14px;\">银行卡最多只允许绑定5张；</span></p><p style=\"box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"font-size: 14px;\">银行卡绑定支持所有主流银行；</span></p><p style=\"box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"font-size: 14px;\">为了用户资金安全，已成功提现的银行卡将自动锁定，无法删除和修改；如想弃用银行卡，可联系在线客服禁用该银行卡；</span></p><p><br/></p>',1530028456,1,22),(36,'每日提现次数','<p style=\"box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"box-sizing: inherit; font-size: 14px;\">为了提供更好的服务体验，降低广大用户提现订单的处理时间，每日最多只允许提现5次，</span></p><p style=\"box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"box-sizing: inherit; font-size: 14px;\">用户当日提款前3次免手续费，</span>第4次开始收取1%手续费，</p><p style=\"box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\">建议用户合理安排每日的提现时间和次数；</p><p><br/></p>',1530028484,1,22),(79,'关于谨防假冒客服人员的通知','<p>关于谨防假冒客服人员的通知关于谨防假冒客服人员的通知关于谨防假冒客服人员的通知关于谨防假冒客服人员的通知关于谨防假冒客服人员的通知关于谨防假冒客服人员的通知关于谨防假冒客服人员的通知关于谨防假冒客服人员的通知关于谨防假冒客服人员的通知关于谨防假冒客服人员的通知关于谨防假冒客服人员的通知关于谨防假冒客服人员的通知关于谨防假冒客服人员的通知关于谨防假冒客服人员的通知关于谨防假冒客服人员的通知关于谨防假冒客服人员的通知关于谨防假冒客服人员的通知</p>',1573203692,1,22),(80,'关于注册','<p>关于注册关于注册关于注册关于注册关于注册关于注册关于注册关于注册关于注册关于注册关于注册关于注册关于注册关于注册关于注册关于注册关于注册关于注册关于注册关于注册</p>',1573203731,1,22),(38,'在线客服时间','<p style=\"box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"box-sizing: inherit; font-size: 14px;\">在线客服提供全天24小时咨询服务，全年无休；</span></p><p style=\"box-sizing: inherit; font-size: 14px; word-wrap: break-word; margin-top: 0px !important; margin-bottom: 0px !important; padding: 0px !important;\"><span style=\"font-size: 14px;\">在游戏中遇到任何问题，欢迎随时咨询我们的在线客服，我们竭诚为您提供最高品质的咨询服务；</span></p><p><br/></p>',1530028537,1,22),(81,'关于添加银行卡','<p>关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡关于添加银行卡</p>',1573203757,1,22),(82,'ceshi','<p>欢迎大家登陆滴滴平台</p>',1573401620,1,22),(83,'代理测试','<p>欢迎来到滴滴代理后台</p>',1573401811,1,22),(84,'滴滴交易商家须知提示','<p style=\"text-indent: 0em;\"><span style=\"font-family:宋体;font-size:14px\">1、</span><span style=\";font-family:宋体;font-size:14px\"><span style=\"font-family:宋体\">没有收到和订单金额相符的转账、一律不要点击确认收款放币！没收到钱就放币，所有损失商家自行承担！</span></span></p><p style=\"text-indent: 0em;\"><span style=\";font-family:宋体;font-size:14px\"><span style=\"font-family:宋体\"><br/></span></span></p><p><span style=\"text-indent: 28px; font-family: 宋体; font-size: 14px;\">2、</span><span style=\"text-indent: 28px; font-family: 宋体; font-size: 14px;\">商家在订单时间内需要保持在线，抢单后请时刻关注收款情况，收到正确的转账需要及时点击收款按钮进行放币。如果在收款5分钟以上未确认放币，客服将会主动联系商家进行确认，并会视情况对商家进行处罚：</span></p><p><span style=\"text-indent: 28px; font-family: 宋体; font-size: 14px;\"><br/></span></p><p><span style=\";font-family:宋体;color:rgb(0,0,255);font-size:14px\"><span style=\"font-family:宋体\">商家积极配合滴滴客服进行订单到账查验，主动反馈收款实际情况的，第一次将给予警告处理，之后每次罚款</span>100DC</span></p><p><span style=\";font-family:宋体;color:rgb(0,0,255);font-size:14px\"><br/></span></p><p><span style=\";font-family:宋体;color:rgb(0,0,255);font-size:14px\"><span style=\"font-family:宋体\">商家不配合滴滴客服进行订单到账查验，隐瞒到账情况，最终申诉结果判定商家已收款的情况，将直接封停商家账号，所有账户资金不予退款。</span></span></p><p><span style=\";font-family:宋体;color:rgb(0,0,255);font-size:14px\"><span style=\"font-family:宋体\"><br/></span></span></p><p><span style=\";font-family:宋体;color:rgb(0,0,255);font-size:14px\"><span style=\"font-family:宋体\">客服在商家抢单时间</span>1<span style=\"font-family:宋体\">小时内多次联系商家未成功，将会强制商家放币，并额外罚款</span><span style=\"font-family:Calibri\">100DC</span></span></p><p><span style=\";font-family:宋体;color:rgb(0,0,255);font-size:14px\"><span style=\"font-family:Calibri\"><br/></span></span></p><p><span style=\"text-indent: 28px; font-family: 宋体; font-size: 14px;\">3、</span><span style=\"text-indent: 28px; font-family: 宋体; font-size: 14px;\">商家资金中会员1000DC是无法进行接单的，比如你有<span style=\"font-family:Calibri\">1300DC</span>，你就最多接一个<span style=\"font-family:Calibri\">300</span>的订单！时刻注意资金可以使用的<span style=\"font-family:Calibri\">DC</span>还有多少。</span></p><p><span style=\"text-indent: 28px; font-family: 宋体; font-size: 14px;\"><br/></span></p><p><span style=\";font-family:宋体;font-size:14px\">4<span style=\"font-family:宋体\">、如果接到多笔订单都没有付款，查看自己的收款码是否受限。</span></span></p>',1573524465,1,22),(85,'1','&lt;p&gt;1&lt;/p&gt;',1575452896,2,31),(86,'1','<p>2</p>',1575453016,2,31),(87,'nihao','<p>sfsdfsdfsad</p>',1575475923,1,26),(88,'dfh','<p>srfaergaerg</p>',1575476034,1,26),(89,'ffff','<p>hjgjhjhjhj</p>',1575482453,1,26);
/*!40000 ALTER TABLE `ly_message` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_notice`
--

DROP TABLE IF EXISTS `ly_notice`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_notice` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL DEFAULT '' COMMENT '公告标题',
  `content` text NOT NULL DEFAULT '' COMMENT '公告内容',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '公告发布时间',
  `gropid` tinyint(4) NOT NULL DEFAULT '1' COMMENT '分类',
  `state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态',
  `cover_img` varchar(255) NOT NULL DEFAULT '',
  `url` varchar(255) NOT NULL DEFAULT '',
  `lang` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=130 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='公告表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_notice`
--

LOCK TABLES `ly_notice` WRITE;
/*!40000 ALTER TABLE `ly_notice` DISABLE KEYS */;
INSERT INTO `ly_notice` VALUES (112,'在线客服','<p>TELEGRAM@cobweyyy</p>',1605004753,1,1,'/upload/resource/cover_img.png','t.me/cobweyyy','cn');
/*!40000 ALTER TABLE `ly_notice` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_notice_group`
--

DROP TABLE IF EXISTS `ly_notice_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_notice_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(50) NOT NULL DEFAULT '',
  `addtime` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `group_name` (`group_name`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_notice_group`
--

LOCK TABLES `ly_notice_group` WRITE;
/*!40000 ALTER TABLE `ly_notice_group` DISABLE KEYS */;
INSERT INTO `ly_notice_group` VALUES (1,'网站公告',1572944093),(2,'帮助中心',1585200022),(7,'123',1728822625);
/*!40000 ALTER TABLE `ly_notice_group` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_order`
--

DROP TABLE IF EXISTS `ly_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '下单的客户Id',
  `pid` int(11) NOT NULL DEFAULT '0' COMMENT '接入商户标识',
  `order_number` varchar(110) NOT NULL DEFAULT '' COMMENT '订单编号',
  `state` tinyint(4) NOT NULL DEFAULT '3' COMMENT '1=完成;2=取消;3=进行中',
  `username` varchar(50) NOT NULL DEFAULT '',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `trade_number` varchar(110) NOT NULL DEFAULT '',
  `daily_income` decimal(8,4) NOT NULL DEFAULT '0.0000' COMMENT '项目返点',
  `rebate` decimal(8,4) NOT NULL DEFAULT '0.0000' COMMENT '会员返点',
  `investment_amount` int(11) NOT NULL DEFAULT '0' COMMENT '投资金额',
  `bearing_day` int(11) NOT NULL DEFAULT '0' COMMENT '计息日',
  `due_day` int(11) NOT NULL DEFAULT '0',
  `agreement` text NOT NULL DEFAULT '' COMMENT '合同',
  `title` varchar(255) DEFAULT NULL COMMENT '项目名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='订单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_order`
--

LOCK TABLES `ly_order` WRITE;
/*!40000 ALTER TABLE `ly_order` DISABLE KEYS */;
/*!40000 ALTER TABLE `ly_order` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_order_record`
--

DROP TABLE IF EXISTS `ly_order_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_order_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '下单的客户Id',
  `pid` int(11) NOT NULL DEFAULT '0' COMMENT '接入商户标识',
  `order_number` varchar(110) NOT NULL DEFAULT '' COMMENT '订单编号',
  `state` tinyint(4) NOT NULL DEFAULT '3' COMMENT '1=完成;2=取消;3=进行中',
  `username` varchar(50) NOT NULL DEFAULT '',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `daily_income` decimal(8,4) NOT NULL DEFAULT '0.0000' COMMENT '项目返点',
  `rebate` decimal(8,4) NOT NULL DEFAULT '0.0000' COMMENT '会员返点',
  `repayment_principal` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '投资金额',
  `trtime` int(11) NOT NULL DEFAULT '0' COMMENT '预计支付日',
  `no` int(11) NOT NULL DEFAULT '0' COMMENT '期号',
  `interest_income` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '预计支付金额',
  `oid` int(11) NOT NULL DEFAULT '0' COMMENT '订单列表id',
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '项目名称',
  `bearing_day` int(11) NOT NULL DEFAULT '0' COMMENT '计息起始日',
  `payment_date` int(11) NOT NULL DEFAULT '0' COMMENT '实际支付日',
  `payment_amount` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '实际支付金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='订单投资表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_order_record`
--

LOCK TABLES `ly_order_record` WRITE;
/*!40000 ALTER TABLE `ly_order_record` DISABLE KEYS */;
/*!40000 ALTER TABLE `ly_order_record` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_qrcode`
--

DROP TABLE IF EXISTS `ly_qrcode`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_qrcode` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
  `codename` varchar(110) NOT NULL DEFAULT '' COMMENT '昵称',
  `codenumder` varchar(110) NOT NULL DEFAULT '' COMMENT '账号',
  `payway` varchar(60) NOT NULL DEFAULT '' COMMENT '支付方式;AliPay(支付宝);WechatPay(微信)',
  `qrcodeurl` varchar(255) NOT NULL DEFAULT '' COMMENT '二维码链接',
  `calltimes` int(11) NOT NULL DEFAULT '0' COMMENT '调用时间',
  `reg_time` int(11) NOT NULL DEFAULT '0' COMMENT '注册时间',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态1=正常;2锁定',
  `remarks` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `defaults` tinyint(1) NOT NULL DEFAULT '0' COMMENT '默认',
  `enable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `paywayurl` varchar(255) NOT NULL DEFAULT '' COMMENT '二维码URL',
  `callnumber` int(11) NOT NULL DEFAULT '0' COMMENT '调用总次数',
  `call_today` int(11) NOT NULL DEFAULT '0' COMMENT '今日次数',
  `price` decimal(16,2) DEFAULT NULL COMMENT '金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户尔维马表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_qrcode`
--

LOCK TABLES `ly_qrcode` WRITE;
/*!40000 ALTER TABLE `ly_qrcode` DISABLE KEYS */;
/*!40000 ALTER TABLE `ly_qrcode` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_recaivables`
--

DROP TABLE IF EXISTS `ly_recaivables`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_recaivables` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bid` int(11) NOT NULL DEFAULT '0' COMMENT '收款账户所属银行（见银行表ID）',
  `name` char(20) NOT NULL DEFAULT '' COMMENT '收款人姓名',
  `account` char(30) NOT NULL DEFAULT '' COMMENT '收款人账号',
  `bank` char(120) NOT NULL DEFAULT '' COMMENT '开户行',
  `type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '转账类型（见支付方式表ID）',
  `qrcode` varchar(255) NOT NULL DEFAULT '' COMMENT '二维码路径',
  `open_level` varchar(50) NOT NULL DEFAULT '' COMMENT '开放等级',
  `state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态。\r\n1：开启；\r\n2：关闭；',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=159 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='收款账户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_recaivables`
--

LOCK TABLES `ly_recaivables` WRITE;
/*!40000 ALTER TABLE `ly_recaivables` DISABLE KEYS */;
/*!40000 ALTER TABLE `ly_recaivables` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_rechange_type`
--

DROP TABLE IF EXISTS `ly_rechange_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_rechange_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` char(30) NOT NULL DEFAULT '' COMMENT '名称',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '充值方式状态。\r\n1：正常；\r\n2：关闭；',
  `code` char(20) NOT NULL DEFAULT '' COMMENT '充值方式编码',
  `type` char(6) NOT NULL DEFAULT '' COMMENT '充值方式所属端',
  `submitUrl` char(100) NOT NULL DEFAULT '' COMMENT '提交地址',
  `minPrice` decimal(16,2) NOT NULL DEFAULT '50.00' COMMENT '最小充值金额',
  `maxPrice` decimal(16,2) NOT NULL DEFAULT '100000.00' COMMENT '最大充值金额',
  `fee` decimal(8,4) NOT NULL DEFAULT '0.0000' COMMENT '手续费',
  `mode` char(20) NOT NULL DEFAULT 'online' COMMENT '充值方式',
  `fixed` varchar(60) NOT NULL DEFAULT '' COMMENT '固定金额',
  `qrcode` varchar(255) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `sort` (`sort`,`type`) USING BTREE,
  UNIQUE KEY `code` (`code`,`type`) USING BTREE,
  KEY `state` (`state`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=117 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='充值方式表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_rechange_type`
--

LOCK TABLES `ly_rechange_type` WRITE;
/*!40000 ALTER TABLE `ly_rechange_type` DISABLE KEYS */;

/*!40000 ALTER TABLE `ly_rechange_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_repayment_method`
--

DROP TABLE IF EXISTS `ly_repayment_method`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_repayment_method` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(50) NOT NULL DEFAULT '' COMMENT '还款方式',
  `Interest_time` varchar(50) NOT NULL DEFAULT '' COMMENT '结息时间说明',
  `state` tinyint(4) NOT NULL DEFAULT '0',
  `day` tinyint(4) NOT NULL DEFAULT '0' COMMENT '理财期限',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `group_name` (`group_name`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='还款方式';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_repayment_method`
--

LOCK TABLES `ly_repayment_method` WRITE;
/*!40000 ALTER TABLE `ly_repayment_method` DISABLE KEYS */;
INSERT INTO `ly_repayment_method` VALUES (1,'每日返息，到期还本','满 24小时 自动结息',1,1),(2,'每周返息，到期还本','满 168小时 自动结息',1,7),(3,'每月返息，到期还本','满 720小时 自动结息',1,30),(4,'一次性还本付息','到期还本，到期付息',1,0),(5,'每日复利，保本保息','满 24小时 自动结息',1,1);
/*!40000 ALTER TABLE `ly_repayment_method` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_setting`
--

DROP TABLE IF EXISTS `ly_setting`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_setting` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `q_server_name` varchar(100) NOT NULL DEFAULT '' COMMENT '前台域名（地址）',
  `h_server_name` varchar(100) NOT NULL DEFAULT '' COMMENT '后台域名（地址）',
  `admin_title` varchar(100) NOT NULL DEFAULT '' COMMENT '商户后台标题',
  `manage_title` varchar(100) NOT NULL DEFAULT '' COMMENT '后台标题',
  `web_title` varchar(20) NOT NULL DEFAULT '',
  `manage_ip_white` tinyint(4) NOT NULL DEFAULT '1' COMMENT '后台是否启用IP白名单.\r\n1:否;\r\n2:是.',
  `ualipay` decimal(8,4) NOT NULL DEFAULT '0.0000' COMMENT '用户支付宝费率',
  `uwechatpay` decimal(8,4) NOT NULL DEFAULT '0.0000' COMMENT '用户微信费率',
  `uunionpay` decimal(8,4) NOT NULL DEFAULT '0.0000' COMMENT '用户云闪付费率',
  `service_hotline` varchar(20) NOT NULL DEFAULT '' COMMENT '服务热线',
  `official_QQ` varchar(20) NOT NULL DEFAULT '' COMMENT '官方QQ',
  `WeChat_official` varchar(255) NOT NULL DEFAULT '' COMMENT '微信公众号',
  `Mobile_client` varchar(255) NOT NULL DEFAULT '' COMMENT '手机客户端',
  `aboutus` text NOT NULL DEFAULT '' COMMENT '关于我们',
  `company` text NOT NULL DEFAULT '' COMMENT '公司资质',
  `contact` text NOT NULL DEFAULT '' COMMENT '联系我们',
  `problem` text NOT NULL DEFAULT '' COMMENT '安全保障',
  `guides` text NOT NULL DEFAULT '' COMMENT '新手指南',
  `hezuomeiti` text NOT NULL DEFAULT '' COMMENT '合作媒体',
  `zhifufangshi` text NOT NULL DEFAULT '' COMMENT '支付方式',
  `record_number` varchar(255) NOT NULL DEFAULT '' COMMENT '备案号',
  `Company_name` varchar(255) NOT NULL DEFAULT '' COMMENT '公司名字',
  `Customer_QQ` varchar(20) NOT NULL DEFAULT '' COMMENT '客服QQ',
  `Accumulated_investment_amount` int(12) NOT NULL DEFAULT '0' COMMENT '投资金额',
  `Conduct_investment_amount` int(12) NOT NULL DEFAULT '0' COMMENT '正在进行中投资金额',
  `Cumulative_expected_earnings` int(12) NOT NULL DEFAULT '0' COMMENT '累计预期赚取收益',
  `registered_smart_investors` int(12) NOT NULL DEFAULT '0' COMMENT '累计注册智慧投资人',
  `seal_img` varchar(255) NOT NULL DEFAULT '' COMMENT '印章',
  `service_url` varchar(255) NOT NULL DEFAULT '' COMMENT '客服链接',
  `reg_init` int(11) NOT NULL DEFAULT '300' COMMENT '用户注册出初始信用',
  `credit_points_lt` int(11) NOT NULL DEFAULT '200' COMMENT '信用分小于该值则限制每日可接任务',
  `credit_points_task` int(11) NOT NULL DEFAULT '1' COMMENT '信用分过低则限制每日可接任务',
  `credit_points_close` int(11) NOT NULL DEFAULT '150' COMMENT '封号信用分',
  `signin_push` int(11) NOT NULL DEFAULT '2' COMMENT '签到加分',
  `first_win_push` int(11) NOT NULL DEFAULT '1' COMMENT '首胜加分',
  `overdue_ded` int(11) NOT NULL DEFAULT '2' COMMENT '逾期扣分',
  `dissatisfy_ded` int(11) NOT NULL DEFAULT '5' COMMENT '不满足扣分',
  `show_credit_interface` tinyint(4) NOT NULL DEFAULT '1' COMMENT '前端是否显示信用界面.1:显示;2:隐藏.',
  `task_phone` varchar(25) DEFAULT NULL COMMENT '平台发布任务的手机号',
  `regment` decimal(16,2) DEFAULT '0.00' COMMENT '注册奖励',
  `register_balance` decimal(16,2) DEFAULT '10000.00' COMMENT '用户注册默认余额',
  `min_w` decimal(16,2) DEFAULT '0.00' COMMENT '最低提现金额',
  `max_w` decimal(16,2) DEFAULT '0.00' COMMENT '最高提现金额',
  `auto_audit` tinyint(4) DEFAULT '2' COMMENT '自动审核 1：是 2：否',
  `cash_status` tinyint(4) DEFAULT '1' COMMENT '自动出款状态。1：开启；2：关闭',
  `info_w` varchar(255) DEFAULT NULL COMMENT '提现公告',
  `reg_url` varchar(150) DEFAULT NULL COMMENT '注册链接',
  `is_sms` tinyint(4) DEFAULT '1' COMMENT '是否要短信',
  `reg_code_num` int(11) DEFAULT '0' COMMENT '注册的人数/天',
  `fengge` varchar(255) NOT NULL DEFAULT '' COMMENT '前端的风格',
  `cn` tinyint(4) NOT NULL DEFAULT '0',
  `ma` tinyint(4) NOT NULL DEFAULT '0' COMMENT '马来语',
  `ft` tinyint(4) NOT NULL DEFAULT '0',
  `en` tinyint(4) NOT NULL DEFAULT '0',
  `yny` tinyint(4) NOT NULL DEFAULT '0',
  `vi` tinyint(4) NOT NULL DEFAULT '0',
  `jp` tinyint(4) NOT NULL DEFAULT '0',
  `es` tinyint(4) NOT NULL DEFAULT '0',
  `ty` tinyint(4) NOT NULL DEFAULT '0',
  `currency` text NOT NULL DEFAULT '',
  `yd` tinyint(4) NOT NULL DEFAULT '0',
  `is_rec_code` tinyint(2) NOT NULL DEFAULT '0',
  `default_language` varchar(32) NOT NULL DEFAULT '',
  `sms_user` varchar(64) NOT NULL DEFAULT '',
  `sms_pwd` varchar(64) NOT NULL DEFAULT '',
  `robot_level` int(11) NOT NULL DEFAULT '0',
  `activity_url` varchar(255) DEFAULT NULL,
  `app_down` varchar(255) DEFAULT NULL COMMENT 'APP下载地址',
  `pt` tinyint(1) NOT NULL DEFAULT '0',
  `w_rate` varchar(20) NOT NULL DEFAULT '',
  `self_first_buy_lottery_times` tinyint(4) NOT NULL DEFAULT '1' COMMENT '自己首次购买VIP抽奖次数奖励',
  `self_upgrade_lottery_times` tinyint(4) NOT NULL DEFAULT '1' COMMENT '自己VIP升级抽奖次数奖励',
  `self_renew_lottery_times` tinyint(4) NOT NULL DEFAULT '0' COMMENT '自己VIP续费/复购抽奖次数奖励',
  `invite_first_buy_lottery_times` tinyint(4) NOT NULL DEFAULT '1' COMMENT '邀请他人首次购买VIP给推荐人的抽奖次数奖励',
  `invite_upgrade_lottery_times` tinyint(4) NOT NULL DEFAULT '0' COMMENT '邀请他人VIP升级给推荐人的抽奖次数奖励',
  `invite_renew_lottery_times` tinyint(4) NOT NULL DEFAULT '0' COMMENT '邀请他人VIP续费/复购给推荐人的抽奖次数奖励',
  `withdrawal_fee_switch` tinyint(4) NOT NULL DEFAULT '1' COMMENT '提现手续费开关 1:开启 2:关闭',
  `withdrawal_bank_fee_rate` decimal(8,4) NOT NULL DEFAULT '0.0000' COMMENT '银行卡提现手续费百分比',
  `weekend_withdrawal_allowed` tinyint(4) NOT NULL DEFAULT '1' COMMENT '周末是否可提现 1:是 2:否',
  `daily_withdrawal_limit` int(11) NOT NULL DEFAULT '0' COMMENT '每天提现次数限制 0:无限制',
  `withdrawal_time_limit_enabled` tinyint(4) NOT NULL DEFAULT '2' COMMENT '提现时间限制开关 1:开启 2:关闭',
  `withdrawal_start_time` varchar(8) NOT NULL DEFAULT '00:00' COMMENT '提现开始时间 格式:HH:MM',
  `withdrawal_end_time` varchar(8) NOT NULL DEFAULT '23:59' COMMENT '提现结束时间 格式:HH:MM',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_setting`
--

LOCK TABLES `ly_setting` WRITE;
/*!40000 ALTER TABLE `ly_setting` DISABLE KEYS */;
INSERT INTO `ly_setting` VALUES (1,'http://www.xyw64.top','/manage/index','SmartNest','SmartNest','SmartNest',2,0.00,0.00,0.00,'************','888888','/upload/resource/wechat_official.png','/upload/resource/mobile_client.png','<p>111</p>','<p>222</p>','<p>333</p>','<p>444</p>','<p>555</p>','<p>666</p>','<p>777</p>','Copyright© 2015-2024','深圳宝田投资有限公司','8888888',0,0,0,0,'/upload/resource/seal_img.jpg','/kefu/index.php?p=chat',60,30,1,0,1,1,1,1,1,'1335874589',0.00,10000.00,5000000.00,0.00,1,1,'1222','http://www.xyw64.top',2,0,'shopp',1,1,1,1,1,1,1,1,1,'USDT',1,1,'en','E10DQW','u2n798',2,'','/app?lang=',1,'1',1,1,0,1,0,0,1,0.00,1,0,2,'00:00','23:59');
/*!40000 ALTER TABLE `ly_setting` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_slide`
--

DROP TABLE IF EXISTS `ly_slide`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_slide` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `img_path` varchar(255) NOT NULL DEFAULT '' COMMENT '图片路径',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态.\r\n1:启用;\r\n2:为启用.',
  `lang` varchar(255) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=84 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='幻灯片';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_slide`
--

LOCK TABLES `ly_slide` WRITE;
/*!40000 ALTER TABLE `ly_slide` DISABLE KEYS */;
INSERT INTO `ly_slide` VALUES (74,'/upload/resource/202412131743293469161978.png',1,'en'),(75,'/upload/resource/202412131743406345272067.png',1,'ft'),(76,'/upload/resource/202412131743498980333673.png',1,'ja'),(77,'/upload/resource/202412131743591596162903.png',1,'id'),(78,'/upload/resource/202412131744089840673049.png',1,'vi'),(79,'/upload/resource/202412131744191511149935.png',1,'es'),(80,'/upload/resource/202412131744291958532085.png',1,'th'),(81,'/upload/resource/202412131744400657315101.png',1,'yd'),(82,'/upload/resource/202412131744511353310300.png',1,'ma'),(83,'/upload/resource/202412131745045096744101.png',1,'pt'),(73,'/upload/resource/202412131743213561720972.png',1,'cn');
/*!40000 ALTER TABLE `ly_slide` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_task`
--

DROP TABLE IF EXISTS `ly_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_task` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '任务标题',
  `task_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '任务类型。\r\n1：供应信息；\r\n2：需求信息；',
  `task_level` tinyint(4) NOT NULL DEFAULT '1' COMMENT '任务等级',
  `task_class` tinyint(4) NOT NULL DEFAULT '1' COMMENT '任务分类。',
  `content` text NOT NULL DEFAULT '' COMMENT '任务简介',
  `purchase_price` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '购买价格',
  `task_commission` decimal(16,2) DEFAULT '0.00' COMMENT '任务佣金',
  `total_price` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '任务总价',
  `total_number` int(11) NOT NULL DEFAULT '0' COMMENT '总数量',
  `receive_number` int(11) NOT NULL DEFAULT '0' COMMENT '已购买的任务数量',
  `link_info` varchar(255) NOT NULL DEFAULT '' COMMENT '链接信息',
  `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '截止日期',
  `finish_condition` char(50) NOT NULL DEFAULT '' COMMENT '完成条件',
  `lang` char(10) NOT NULL DEFAULT 'cn' COMMENT '语言。1：中文（CN）2：英文',
  `examine_demo` text NOT NULL DEFAULT '' COMMENT '审核样例',
  `task_step` text NOT NULL DEFAULT '' COMMENT '任务步骤',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '发布时间',
  `status` tinyint(4) NOT NULL DEFAULT '3' COMMENT '任务状态。\r\n1：审核中；\r\n2：未通过；\r\n3：进行中；\r\n4：已完成；\r\n5：已放弃；\r\n',
  `remarks` text NOT NULL DEFAULT '' COMMENT '管理员备注',
  `person_time` int(11) NOT NULL DEFAULT '1' COMMENT '每人可购买任务次数。次/人',
  `uid` int(11) NOT NULL DEFAULT '0',
  `username` varchar(50) NOT NULL DEFAULT '',
  `task_pump` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '抽水',
  `surplus_number` int(11) NOT NULL DEFAULT '0' COMMENT '剩余任务数',
  `pump` decimal(8,4) NOT NULL DEFAULT '0.0000',
  `order_number` varchar(50) NOT NULL DEFAULT '',
  `trade_number` varchar(50) NOT NULL DEFAULT '',
  `complete_time` int(11) NOT NULL DEFAULT '0' COMMENT '完成时间',
  `requirement` varchar(255) DEFAULT NULL COMMENT '上传要求',
  `task_rebate1` decimal(8,4) NOT NULL DEFAULT '10.0000' COMMENT '一级分佣比例(%)',
  `task_rebate2` decimal(8,4) NOT NULL DEFAULT '5.0000' COMMENT '二级分佣比例(%)',
  `task_rebate3` decimal(8,4) NOT NULL DEFAULT '2.0000' COMMENT '三级分佣比例(%)',
  `main_image` varchar(500) DEFAULT '' COMMENT '任务主图URL',
  `detail_image` text COMMENT '任务详情图JSON数组',
  `is_visible` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否显示：1=显示，0=隐藏',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `id` (`id`) USING BTREE,
  KEY `task_level` (`task_level`) USING BTREE,
  KEY `task_class` (`task_class`) USING BTREE,
  KEY `end_time` (`end_time`) USING BTREE,
  KEY `add_time` (`add_time`) USING BTREE,
  KEY `uid` (`uid`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `idx_task_rebate` (`task_rebate1`, `task_rebate2`, `task_rebate3`) USING BTREE,
  KEY `idx_main_image` (`main_image`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_task`
--

LOCK TABLES `ly_task` WRITE;
/*!40000 ALTER TABLE `ly_task` DISABLE KEYS */;
/*!40000 ALTER TABLE `ly_task` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_task_class`
--

DROP TABLE IF EXISTS `ly_task_class`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_task_class` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(50) NOT NULL DEFAULT '',
  `group_name_en` varchar(255) NOT NULL DEFAULT '',
  `group_name_ft` varchar(255) NOT NULL DEFAULT '',
  `group_name_ry` varchar(255) NOT NULL DEFAULT '',
  `group_name_xby` varchar(255) NOT NULL DEFAULT '',
  `group_name_ydn` varchar(255) NOT NULL DEFAULT '',
  `group_name_yn` varchar(255) NOT NULL DEFAULT '',
  `state` int(11) NOT NULL DEFAULT '0',
  `group_info` varchar(255) NOT NULL DEFAULT '' COMMENT '说明',
  `group_info_ft` varchar(255) NOT NULL DEFAULT '',
  `group_info_en` varchar(255) NOT NULL DEFAULT '',
  `group_info_ry` varchar(255) NOT NULL DEFAULT '',
  `group_info_xby` varchar(255) NOT NULL DEFAULT '',
  `group_info_ydn` varchar(255) NOT NULL DEFAULT '',
  `group_info_yn` varchar(255) NOT NULL DEFAULT '',
  `icon` varchar(255) NOT NULL DEFAULT '' COMMENT '图标',
  `lang` char(20) NOT NULL DEFAULT '',
  `num` tinyint(4) DEFAULT NULL COMMENT '排序',
  `h_group_name` varchar(255) DEFAULT NULL,
  `h_group_info` varchar(255) DEFAULT NULL,
  `h_icon` varchar(255) DEFAULT NULL,
  `is_fx` tinyint(4) DEFAULT '0' COMMENT '是否分享',
  `is_f` tinyint(4) DEFAULT '1' COMMENT '是否发布',
  `group_name_ty` varchar(255) NOT NULL DEFAULT '',
  `group_info_ty` varchar(255) NOT NULL DEFAULT '',
  `group_name_yd` varchar(255) NOT NULL DEFAULT '',
  `group_info_yd` varchar(255) NOT NULL DEFAULT '',
  `group_name_ma` varchar(255) NOT NULL DEFAULT '',
  `group_info_ma` varchar(255) NOT NULL DEFAULT '',
  `group_name_pt` varchar(255) NOT NULL DEFAULT '',
  `group_info_pt` varchar(255) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `group_name` (`group_name`) USING BTREE,
  UNIQUE KEY `id` (`id`) USING BTREE,
  KEY `state` (`state`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=27 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='项目分类';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_task_class`
--

LOCK TABLES `ly_task_class` WRITE;
/*!40000 ALTER TABLE `ly_task_class` DISABLE KEYS */;
INSERT INTO `ly_task_class` (`id`, `group_name`, `group_name_en`, `group_name_ft`, `group_name_ry`, `group_name_xby`, `group_name_ydn`, `group_name_yn`, `state`, `group_info`, `group_info_ft`, `group_info_en`, `group_info_ry`, `group_info_xby`, `group_info_ydn`, `group_info_yn`, `icon`, `lang`, `num`, `h_group_name`, `h_group_info`, `h_icon`, `is_fx`, `is_f`, `group_name_ty`, `group_info_ty`, `group_name_yd`, `group_info_yd`, `group_name_ma`, `group_info_ma`, `group_name_pt`, `group_info_pt`) VALUES
(28, 'VIP1', 'VIP1', 'VIP1', 'VIP1', 'VIP1', 'VIP1', 'VIP1', 1, '', '', '', '', '', '', '', '', '', 2, NULL, NULL, '', 2, 1, 'VIP1', '', 'VIP1', '', 'VIP1', '', 'VIP1', ''),
(29, 'VIP2', 'VIP2', 'VIP2', 'VIP2', 'VIP2', 'VIP2', '', 1, '', '', '', '', '', '', '', '', '', 3, NULL, NULL, '', 2, 1, 'VIP2', '', 'VIP2', '', 'VIP2', '', 'VIP2', ''),
(30, 'VIP3', 'VIP3', 'VIP3', 'VIP3', 'VIP3', 'VIP3', 'VIP3', 1, '', '', '', '', '', '', '', '', '', 4, NULL, NULL, '', 2, 1, 'VIP3', '', 'VIP3', '', 'VIP3', '', 'VIP3', ''),
(31, 'VIP4', 'VIP4', 'VIP4', 'VIP4', 'VIP4', 'VIP4', 'VIP4', 1, '', '', '', '', '', '', '', '', '', 5, NULL, NULL, '', 2, 1, 'VIP4', '', 'VIP4', '', 'VIP4', '', 'VIP4', ''),
(32, 'VIP5', 'VIP5', 'VIP5', 'VIP5', 'VIP5', 'VIP5', 'VIP5', 1, '', '', '', '', '', '', '', '', '', 6, NULL, NULL, '', 2, 1, 'VIP5', '', 'VIP5', '', 'VIP5', '', 'VIP5', ''),
(33, 'VIP6', 'VIP6', 'VIP6', 'VIP6', 'VIP6', 'VIP6', 'VIP6', 1, '', '', '', '', '', '', '', '', '', 7, NULL, NULL, '', 2, 1, 'VIP6', '', 'VIP6', '', 'VIP6', '', 'VIP6', ''),
(27, 'VIP0', 'VIP0', 'VIP0', 'VIP0', 'VIP0', 'VIP0', 'VIP0', 1, '', '', '', '', '', '', '', '', '', 1, NULL, NULL, '', 1, 1, 'VIP0', '', 'VIP0', '', 'VIP0', '', 'VIP0', ''),
(34, 'VIP7', 'VIP7', 'VIP7', 'VIP7', 'VIP7', 'VIP7', 'VIP7', 1, '', '', '', '', '', '', '', '', '', 8, NULL, NULL, '', 2, 1, 'VIP7', '', 'VIP7', '', 'VIP7', '', 'VIP7', ''),
(35, 'VIP8', 'VIP8', 'VIP8', 'VIP8', 'VIP8', 'VIP8', 'VIP8', 1, '', '', '', '', '', '', '', '', '', 9, NULL, NULL, '', 2, 1, 'VIP8', '', 'VIP8', '', 'VIP8', '', 'VIP8', '');
/*!40000 ALTER TABLE `ly_task_class` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_task_tpl`
--

DROP TABLE IF EXISTS `ly_task_tpl`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_task_tpl` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL DEFAULT '',
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '任务标题',
  `task_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '任务类型。\r\n1：供应信息；\r\n2：需求信息；',
  `task_level` tinyint(4) NOT NULL DEFAULT '1' COMMENT '任务等级',
  `task_class` tinyint(4) NOT NULL DEFAULT '1' COMMENT '任务分类。',
  `content` text NOT NULL DEFAULT '' COMMENT '任务简介',
  `purchase_price` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '购买价格',
  `task_commission` decimal(16,2) DEFAULT '0.00' COMMENT '任务佣金',
  `total_price` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '任务总价',
  `total_number` int(11) NOT NULL DEFAULT '0' COMMENT '总数量',
  `receive_number` int(11) NOT NULL DEFAULT '0' COMMENT '已购买的任务数量',
  `link_info` varchar(255) NOT NULL DEFAULT '' COMMENT '链接信息',
  `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '截止日期',
  `finish_condition` char(50) NOT NULL DEFAULT '' COMMENT '完成条件',
  `lang` char(10) NOT NULL DEFAULT 'cn' COMMENT '语言。1：中文（CN）2：英文',
  `examine_demo` text NOT NULL DEFAULT '' COMMENT '审核样例',
  `task_step` text NOT NULL DEFAULT '' COMMENT '任务步骤',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '发布时间',
  `status` tinyint(4) NOT NULL DEFAULT '3' COMMENT '任务状态。\r\n1：审核中；\r\n2：未通过；\r\n3：进行中；\r\n4：已完成；\r\n5：已放弃；\r\n',
  `remarks` text NOT NULL DEFAULT '' COMMENT '管理员备注',
  `person_time` int(11) NOT NULL DEFAULT '1' COMMENT '每人可购买任务次数。次/人',
  `uid` int(11) NOT NULL DEFAULT '0',
  `username` varchar(50) NOT NULL DEFAULT '',
  `task_pump` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '抽水',
  `surplus_number` int(11) NOT NULL DEFAULT '0' COMMENT '剩余任务数',
  `pump` decimal(8,4) NOT NULL DEFAULT '0.0000',
  `order_number` varchar(50) NOT NULL DEFAULT '',
  `trade_number` varchar(50) NOT NULL DEFAULT '',
  `complete_time` int(11) NOT NULL DEFAULT '0' COMMENT '完成时间',
  `requirement` varchar(255) DEFAULT NULL COMMENT '上传要求',
  `task_rebate1` decimal(8,4) NOT NULL DEFAULT '10.0000' COMMENT '一级分佣比例(%)',
  `task_rebate2` decimal(8,4) NOT NULL DEFAULT '5.0000' COMMENT '二级分佣比例(%)',
  `task_rebate3` decimal(8,4) NOT NULL DEFAULT '2.0000' COMMENT '三级分佣比例(%)',
  `main_image` varchar(500) DEFAULT '' COMMENT '任务主图URL',
  `detail_image` text COMMENT '任务详情图JSON数组',
  `is_visible` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否显示：1=显示，0=隐藏',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `id` (`id`) USING BTREE,
  KEY `task_level` (`task_level`) USING BTREE,
  KEY `task_class` (`task_class`) USING BTREE,
  KEY `end_time` (`end_time`) USING BTREE,
  KEY `add_time` (`add_time`) USING BTREE,
  KEY `uid` (`uid`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `idx_task_rebate` (`task_rebate1`, `task_rebate2`, `task_rebate3`) USING BTREE,
  KEY `idx_main_image` (`main_image`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_task_tpl`
--

LOCK TABLES `ly_task_tpl` WRITE;
/*!40000 ALTER TABLE `ly_task_tpl` DISABLE KEYS */;
INSERT INTO `ly_task_tpl` VALUES
(8,'普通会员','Browse',1,1,24,'Browse plus screenshots',0.01,0.01,999.99,99999,0,'',1761840000,'','cn','','[{\"img\":\"\",\"describe\":\"\"}]',1728894926,3,'',1,0,'17837453157',0.00,1,0.00,'B202410141635262927235298','L202410141635262932567994',0,'',10.00,5.00,2.00,'','',1),
(9,'白银会员','Browse',1,2,24,'Browse plus screenshots',0.01,0.01,999.99,99999,0,'',1760976000,'','cn','','[{\"img\":\"\",\"describe\":\"\"}]',1729520748,3,'',1,0,'18937454765',0.00,99999,0.00,'B202410212225487354006821','L202410212225487316587562',0,'',10.00,5.00,2.00,'','',1);
/*!40000 ALTER TABLE `ly_task_tpl` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_teammove_log`
--

DROP TABLE IF EXISTS `ly_teammove_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_teammove_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '操作员',
  `log` varchar(255) NOT NULL DEFAULT '' COMMENT '操作内容',
  `addtime` int(11) NOT NULL DEFAULT '0' COMMENT '操作时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='团队迁移日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_teammove_log`
--

LOCK TABLES `ly_teammove_log` WRITE;
/*!40000 ALTER TABLE `ly_teammove_log` DISABLE KEYS */;
INSERT INTO `ly_teammove_log` VALUES (1,24,'迁移123123至13800000000',1601898783),(2,24,'迁移123123123至13800000000',1604986827);
/*!40000 ALTER TABLE `ly_teammove_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_trade_details`
--

DROP TABLE IF EXISTS `ly_trade_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_trade_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `username` char(30) NOT NULL DEFAULT '' COMMENT '用户名',
  `user_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '用户类型',
  `sid` int(11) NOT NULL DEFAULT '0' COMMENT '转账目标ID',
  `source_uid` int(11) NOT NULL DEFAULT '0' COMMENT '来源用户ID',
  `source_username` char(30) NOT NULL DEFAULT '' COMMENT '来源用户名',
  `order_number` char(60) NOT NULL DEFAULT '' COMMENT '订单号',
  `trade_number` char(60) NOT NULL DEFAULT '' COMMENT '交易号',
  `trade_time` int(11) NOT NULL DEFAULT '0' COMMENT '交易时间',
  `trade_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '交易类型。',
  `trade_amount` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '交易金额',
  `trade_before_balance` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '交易前余额',
  `account_balance` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '账户余额',
  `remarks` text NOT NULL DEFAULT '' COMMENT '备注（中文）',
  `remarks_en` text NOT NULL DEFAULT '' COMMENT '备注（英文）',
  `remarks_id` text NOT NULL DEFAULT '' COMMENT '备注（印尼语）',
  `remarks_ft` text NOT NULL DEFAULT '' COMMENT '备注（繁体中文）',
  `remarks_yd` text NOT NULL DEFAULT '' COMMENT '备注（印度语）',
  `remarks_vi` text NOT NULL DEFAULT '' COMMENT '备注（越南语）',
  `remarks_es` text NOT NULL DEFAULT '' COMMENT '备注（西班牙语）',
  `remarks_ja` text NOT NULL DEFAULT '' COMMENT '备注（日语）',
  `remarks_th` text NOT NULL DEFAULT '' COMMENT '备注（泰语）',
  `remarks_ma` text NOT NULL DEFAULT '' COMMENT '备注（马来语）',
  `remarks_pt` text NOT NULL DEFAULT '' COMMENT '备注（葡萄牙语）',
  `vip_level` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'VIP等级（排序使用）',
  `state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态。\r\n1：成功；\r\n2：失败；\r\n3：审核中；',
  `isadmin` tinyint(4) NOT NULL DEFAULT '2' COMMENT '是否管理员操作。\r\n1：是；\r\n2：否；',
  `types` tinyint(4) NOT NULL DEFAULT '1' COMMENT '流水类型1=用户2=商户',
  `account_total_balance` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '交易后总资产',
  `front_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '前端显示交易类型',
  `payway` varchar(30) NOT NULL DEFAULT '' COMMENT '支付方式',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `trade_number` (`trade_number`) USING BTREE,
  KEY `uid` (`uid`) USING BTREE,
  KEY `vip_level` (`vip_level`) USING BTREE,
  KEY `state` (`state`) USING BTREE,
  KEY `trade_time` (`trade_time`) USING BTREE,
  KEY `trade_type` (`trade_type`) USING BTREE,
  KEY `order_number` (`order_number`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=475724 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='流水表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_trade_details`
--

LOCK TABLES `ly_trade_details` WRITE;
/*!40000 ALTER TABLE `ly_trade_details` DISABLE KEYS */;
/*!40000 ALTER TABLE `ly_trade_details` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_user_activity`
--

DROP TABLE IF EXISTS `ly_user_activity`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_user_activity` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `date` int(11) NOT NULL DEFAULT '0' COMMENT '日期',
  `order_number` char(25) NOT NULL DEFAULT '' COMMENT '订单号',
  `bet_total` decimal(16,4) NOT NULL DEFAULT '0.0000' COMMENT '当日投注总额',
  `rebate` decimal(16,4) NOT NULL DEFAULT '0.0000' COMMENT '返点值',
  `price` decimal(16,4) NOT NULL DEFAULT '0.0000' COMMENT '金额',
  `state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态。\r\n1：未领取；\r\n2：已领取；',
  `set_time` int(11) NOT NULL DEFAULT '0' COMMENT '领取时间',
  `trade_number` char(25) NOT NULL DEFAULT '' COMMENT '流水号',
  `sign` varchar(120) NOT NULL DEFAULT '' COMMENT '平台标识',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `order_number` (`order_number`) USING BTREE,
  KEY `uid` (`uid`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户活动记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_user_activity`
--

LOCK TABLES `ly_user_activity` WRITE;
/*!40000 ALTER TABLE `ly_user_activity` DISABLE KEYS */;
/*!40000 ALTER TABLE `ly_user_activity` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_user_bank`
--

DROP TABLE IF EXISTS `ly_user_bank`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_user_bank` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `name` char(30) NOT NULL DEFAULT '' COMMENT '持卡人姓名',
  `card_no` char(200) NOT NULL DEFAULT '' COMMENT '银行卡账户',
  `bid` int(11) NOT NULL DEFAULT '0' COMMENT '银行id',
  `bank_name` char(30) NOT NULL DEFAULT '' COMMENT '银行名称',
  `bank_branch_name` char(30) NOT NULL DEFAULT '' COMMENT '支行名称',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1正常；2锁定；3删除',
  `last_use_time` int(11) NOT NULL DEFAULT '0' COMMENT '调用时间',
  `defaults` tinyint(1) NOT NULL DEFAULT '0' COMMENT '默认',
  `enable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `uid` (`uid`) USING BTREE,
  KEY `add_time` (`add_time`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=339 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户银行表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_user_bank`
--

LOCK TABLES `ly_user_bank` WRITE;
/*!40000 ALTER TABLE `ly_user_bank` DISABLE KEYS */;
INSERT INTO `ly_user_bank` VALUES (338,100,'sixuooasx','lietu',0,'USDT','',**********,'',1,0,0,1),(225,100,'sixuooasx','TVWXJfKAmfvzvSvLb7Ay7M77P8X8c2JZDS',0,'USDT','',**********,'',1,0,0,1);
/*!40000 ALTER TABLE `ly_user_bank` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_user_commission`
--

DROP TABLE IF EXISTS `ly_user_commission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_user_commission` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `gid` int(11) NOT NULL DEFAULT '0' COMMENT '发放人',
  `order_number` char(60) NOT NULL DEFAULT '' COMMENT '订单号',
  `date` int(11) NOT NULL DEFAULT '0' COMMENT '发放日期',
  `team_order` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '团队业绩',
  `ratio` decimal(16,2) NOT NULL DEFAULT '0.00',
  `price` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '金额',
  `issue_time` int(11) NOT NULL DEFAULT '0' COMMENT '发放时间',
  `status` tinyint(4) NOT NULL DEFAULT '2' COMMENT '状态。\r\n1：成功；\r\n2：失败；',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 ROW_FORMAT=FIXED;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_user_commission`
--

LOCK TABLES `ly_user_commission` WRITE;
/*!40000 ALTER TABLE `ly_user_commission` DISABLE KEYS */;
INSERT INTO `ly_user_commission` VALUES (1,1,0,'',0,0.00,0.00,0.00,1232323,2);
/*!40000 ALTER TABLE `ly_user_commission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_user_credit`
--

DROP TABLE IF EXISTS `ly_user_credit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_user_credit` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL DEFAULT '' COMMENT '名称用户',
  `uid` int(11) NOT NULL DEFAULT '0',
  `credit` int(11) DEFAULT NULL COMMENT '信用',
  `time` int(11) DEFAULT NULL COMMENT '时间',
  `remarks` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `uid` (`uid`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=FIXED COMMENT='用户信用记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_user_credit`
--

LOCK TABLES `ly_user_credit` WRITE;
/*!40000 ALTER TABLE `ly_user_credit` DISABLE KEYS */;
/*!40000 ALTER TABLE `ly_user_credit` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_user_daily`
--

DROP TABLE IF EXISTS `ly_user_daily`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_user_daily` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `username` char(30) NOT NULL DEFAULT '' COMMENT '用户名',
  `user_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '用户类型',
  `date` int(11) NOT NULL DEFAULT '0' COMMENT '日期',
  `recharge` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '充值',
  `withdrawal` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '提现',
  `task` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '发布任务',
  `rebate` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '下级级返点',
  `regment` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '活动',
  `is_commission` tinyint(4) NOT NULL DEFAULT '2' COMMENT '是否发放成功。\r\n1：是；\r\n2：否；',
  `other` decimal(16,2) NOT NULL DEFAULT '0.00',
  `buymembers` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '购买会员',
  `spread` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '推广奖励',
  `pump` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '平台抽水',
  `revoke` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '撤销任务',
  `commission` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '任务提成',
  `transfer_c` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '转账转出',
  `transfer_r` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '转账转入',
  `l_t_o_n` int(11) DEFAULT '0' COMMENT '购买任务数',
  `w_t_o_n` int(11) DEFAULT '0' COMMENT '完成任务数',
  `s_t_o_n` int(11) DEFAULT '0' COMMENT '失败任务数',
  `e_t_o_n` int(11) DEFAULT '0' COMMENT '恶意任务数',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uiddate` (`uid`,`date`) USING BTREE,
  KEY `uid` (`uid`) USING BTREE,
  KEY `date` (`date`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=4332 DEFAULT CHARSET=utf8 ROW_FORMAT=FIXED COMMENT='用户每日数据表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_user_daily`
--

LOCK TABLES `ly_user_daily` WRITE;
/*!40000 ALTER TABLE `ly_user_daily` DISABLE KEYS */;
INSERT INTO `ly_user_daily` VALUES (67,100,'22',1,**********,0.00,0.00,0.00,0.00,0.00,2,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,76,0,0,0);
/*!40000 ALTER TABLE `ly_user_daily` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_user_grade`
--

DROP TABLE IF EXISTS `ly_user_grade`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_user_grade` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT 'vip名称',
  `grade` tinyint(4) NOT NULL DEFAULT '0' COMMENT '等级',
  `amount` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '需要的充值金额',
  `state` tinyint(4) NOT NULL DEFAULT '1',
  `is_hidden` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否隐藏：0=显示，1=隐藏',
  `is_locked` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否锁定：0=可购买，1=锁定不可购买',
  `en_name` varchar(255) NOT NULL DEFAULT '',
  `ft_name` varchar(255) NOT NULL DEFAULT '',
  `ry_name` varchar(255) NOT NULL DEFAULT '',
  `xby_name` varchar(255) NOT NULL DEFAULT '',
  `ydn_name` varchar(255) NOT NULL DEFAULT '',
  `yn_name` varchar(255) NOT NULL DEFAULT '',
  `ty_name` varchar(255) NOT NULL DEFAULT '',
  `yd_name` varchar(255) NOT NULL DEFAULT '',
  `ma_name` varchar(255) NOT NULL DEFAULT '' COMMENT '马来语',
  `pt_name` varchar(255) NOT NULL DEFAULT '',
  `number` int(11) NOT NULL DEFAULT '0' COMMENT '可购买任务次数',
  `pump` decimal(8,4) NOT NULL DEFAULT '0.0000' COMMENT '抽水(已废弃，改为任务级别设置)',
  `spread` varchar(255) DEFAULT NULL COMMENT '推荐返佣(已废弃，改为任务级别设置)',
  `profits` varchar(50) DEFAULT NULL,
  `validity_time` int(11) DEFAULT '12' COMMENT '有效期',
  `daily_sign` varchar(50) NOT NULL DEFAULT '',
  `daily_turntable_times` int(11) NOT NULL DEFAULT '0',
  `invite_rebate1` decimal(8,4) DEFAULT '0.0000' COMMENT '邀请分佣一级比例(%)',
  `invite_rebate2` decimal(8,4) DEFAULT '0.0000' COMMENT '邀请分佣二级比例(%)',
  `invite_rebate3` decimal(8,4) DEFAULT '0.0000' COMMENT '邀请分佣三级比例(%)',
  `privilege_description` text DEFAULT NULL COMMENT '特权说明(中文)',
  `privilege_description_en` text DEFAULT NULL COMMENT '特权说明(英文)',
  `privilege_description_ft` text DEFAULT NULL COMMENT '特权说明(繁体)',
  `privilege_description_ry` text DEFAULT NULL COMMENT '特权说明(日文)',
  `privilege_description_xby` text DEFAULT NULL COMMENT '特权说明(西班牙文)',
  `privilege_description_ydn` text DEFAULT NULL COMMENT '特权说明(印尼文)',
  `privilege_description_yn` text DEFAULT NULL COMMENT '特权说明(越南文)',
  `privilege_description_ty` text DEFAULT NULL COMMENT '特权说明(泰文)',
  `privilege_description_yd` text DEFAULT NULL COMMENT '特权说明(印度文)',
  `privilege_description_ma` text DEFAULT NULL COMMENT '特权说明(马来文)',
  `privilege_description_pt` text DEFAULT NULL COMMENT '特权说明(葡萄牙文)',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `grade` (`grade`) USING BTREE,
  KEY `state` (`state`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=17 DEFAULT CHARSET=utf8 ROW_FORMAT=FIXED COMMENT='用户团队表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_user_grade`
--

LOCK TABLES `ly_user_grade` WRITE;
/*!40000 ALTER TABLE `ly_user_grade` DISABLE KEYS */;

INSERT INTO `ly_user_grade` (`id`, `name`, `grade`, `amount`, `state`, `is_hidden`, `is_locked`, `en_name`, `ft_name`, `ry_name`, `xby_name`, `ydn_name`, `yn_name`, `ty_name`, `yd_name`, `ma_name`, `pt_name`, `number`, `pump`, `spread`, `profits`, `validity_time`, `daily_sign`, `daily_turntable_times`, `invite_rebate1`, `invite_rebate2`, `invite_rebate3`, `privilege_description`, `privilege_description_en`, `privilege_description_ft`, `privilege_description_ry`, `privilege_description_xby`, `privilege_description_ydn`, `privilege_description_yn`, `privilege_description_ty`, `privilege_description_yd`, `privilege_description_ma`, `privilege_description_pt`) VALUES
(24, 'VIP7', 8, '1000.00', 1, 0, 0, 'VIP7', 'VIP7', 'VIP7', 'VIP7', 'VIP7', 'VIP7', 'VIP7', 'VIP7', 'VIP7', 'VIP7', 1, '0.0000', NULL, NULL, 12, '0', 0, '10.0000', '3.0000', '1.0000', '', '', '', '', '', '', '', '', '', '', ''),
(25, 'VIP8', 9, '200000000.00', 1, 0, 0, 'VIP8', 'VIP8', 'VIP8', 'VIP8', 'VIP8', 'VIP8', 'VIP8', 'VIP8', 'VIP8', 'VIP8', 5, '0.0000', NULL, NULL, 12, '0', 5, '10.0000', '3.0000', '1.0000', '<p><strong><span style=\"text-wrap-mode: nowrap;\">1.VIP8专属特权</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">升级费用：RP 200,000,000.00</span></p><p><span style=\"text-wrap-mode: nowrap;\">会员有效期1年</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">2.每日任务限制</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">每天最多可完成5个任务</span></p><p><span style=\"text-wrap-mode: nowrap;\">抓住更多赚钱机会，任务越多，收益越丰厚！</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">3.每日任务佣金</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">每个任务的佣金在RP 2,500,000至RP 5,000,000</span></p><p><span style=\"text-wrap-mode: nowrap;\">每月可获得佣金375000000至750000000</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">4.每日抽奖机会</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">每天可获得5次抽奖机会</span></p><p><span style=\"text-wrap-mode: nowrap;\">更多机会，带来更多惊喜！</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">5.下属推荐奖励</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">1级下属推荐奖励10%</span></p><p><span style=\"text-wrap-mode: nowrap;\">2级下属推荐奖励3%</span></p><p><span style=\"text-wrap-mode: nowrap;\">3级下属推荐奖励1%</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">6.下属订单佣金</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">1级下属订单返佣5%</span></p><p><span style=\"text-wrap-mode: nowrap;\">2级下属订单返佣2%</span></p><p><span style=\"text-wrap-mode: nowrap;\">3级下属订单返佣1%</span></p><p><br/></p>', '<p><strong><span style=\"text-wrap-mode: nowrap;\">1.VIP8 Exclusive Privileges</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Upgrade Fee: RP 200,000,000.00</span></p><p><span style=\"text-wrap-mode: nowrap;\">Membership Validity: 1 Year</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">2.Daily Task Limit</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">You can complete up to 5 tasks each day.</span></p><p><span style=\"text-wrap-mode: nowrap;\">Seize more opportunities to earn; the more tasks&nbsp;</span></p><p><span style=\"text-wrap-mode: nowrap;\">you complete, the greater your rewards!</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">3.Daily Task Commission</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">The commission for each task ranges from&nbsp;</span></p><p><span style=\"text-wrap-mode: nowrap;\">RP 2,500,000 to RP 5,000,000.</span></p><p><span style=\"text-wrap-mode: nowrap;\">You can earn a monthly commission of&nbsp;</span></p><p><span style=\"text-wrap-mode: nowrap;\">RP 375,000,000 to RP 750,000,000.</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">4.Daily Lottery Opportunities</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">You can receive 5 lottery chances every day.</span></p><p><span style=\"text-wrap-mode: nowrap;\">More opportunities mean more surprises!</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">5.Subordinate Referral Rewards</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">1st Level Subordinate Referral Reward: 10%</span></p><p><span style=\"text-wrap-mode: nowrap;\">2nd Level Subordinate Referral Reward: 3%</span></p><p><span style=\"text-wrap-mode: nowrap;\">3rd Level Subordinate Referral Reward: 1%</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">6.Subordinate Order Commissions</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">1st Level Subordinate Order Rebate: 5%</span></p><p><span style=\"text-wrap-mode: nowrap;\">2nd Level Subordinate Order Rebate: 2%</span></p><p><span style=\"text-wrap-mode: nowrap;\">3rd Level Subordinate Order Rebate: 1%</span></p><p><br/></p>', '', '', '', '<p><strong><span style=\"text-wrap-mode: nowrap;\">1.Privilege Eksklusif VIP8</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Biaya Upgrade: RP 200.000.000,00</span></p><p><span style=\"text-wrap-mode: nowrap;\">Masa Berlaku Keanggotaan: 1 Tahun</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">2.Batas Tugas Harian</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Anda dapat menyelesaikan hingga 5 tugas setiap hari.</span></p><p><span style=\"text-wrap-mode: nowrap;\">Ambil lebih banyak kesempatan untuk menghasilkan;</span></p><p><span style=\"text-wrap-mode: nowrap;\">&nbsp;semakin banyak tugas yang Anda selesaikan,&nbsp;</span></p><p><span style=\"text-wrap-mode: nowrap;\">semakin besar imbalan Anda!</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">3.Komisi Tugas Harian</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Komisi untuk setiap tugas berkisar antara&nbsp;</span></p><p><span style=\"text-wrap-mode: nowrap;\">RP 2.500.000 hingga RP 5.000.000.</span></p><p><span style=\"text-wrap-mode: nowrap;\">Anda dapat memperoleh komisi bulanan antara&nbsp;</span></p><p><span style=\"text-wrap-mode: nowrap;\">RP 375.000.000 hingga RP 750.000.000.</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">4.Kesempatan Undian Harian</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Anda dapat menerima 5 kesempatan undian setiap hari.</span></p><p><span style=\"text-wrap-mode: nowrap;\">Lebih banyak kesempatan berarti lebih banyak kejutan!</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">5.Hadiah Rujukan Bawahan</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Hadiah Rujukan Bawahan Tingkat 1: 10%</span></p><p><span style=\"text-wrap-mode: nowrap;\">Hadiah Rujukan Bawahan Tingkat 2: 3%</span></p><p><span style=\"text-wrap-mode: nowrap;\">Hadiah Rujukan Bawahan Tingkat 3: 1%</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">6.Komisi Pesanan Bawahan</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Rebate Pesanan Bawahan Tingkat 1: 5%</span></p><p><span style=\"text-wrap-mode: nowrap;\">Rebate Pesanan Bawahan Tingkat 2: 2%</span></p><p><span style=\"text-wrap-mode: nowrap;\">Rebate Pesanan Bawahan Tingkat 3: 1%</span></p><p><br/></p>', '', '', '', '', ''),
(17, 'VIP0', 1, '0.00', 1, 0, 0, 'VIP0', 'VIP0', 'VIP0', 'VIP0', 'VIP0', 'VIP0', 'VIP0', 'VIP0', 'VIP0', 'VIP0', 1, '0.0000', NULL, NULL, '0.1', '0', 0, '0.0000', '0.0000', '0.0000', '<p><span style=\"font-size: 16px;\"><strong><span style=\"text-wrap-mode: nowrap;\">1.VIP0福利</span></strong></span></p><p><span style=\"text-wrap-mode: nowrap; font-size: 16px;\">&nbsp; &nbsp;注册送10000RP</span></p><p><span style=\"text-wrap-mode: nowrap; font-size: 16px;\">&nbsp; &nbsp;每天完成1个任务</span></p><p><span style=\"font-size: 16px;\"><span style=\"text-wrap-mode: nowrap; font-size: 18px;\">&nbsp; &nbsp;3天可获得40000RP</span><span style=\"font-size: 16px; white-space: pre;\"></span></span></p><p><br/></p>', '<p style=\"--tw-border-spacing-x: 0; --tw-border-spacing-y: 0; --tw-translate-x: 0; --tw-translate-y: 0; --tw-rotate: 0; --tw-skew-x: 0; --tw-skew-y: 0; --tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgba(69,89,164,.5); --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; border: 0px solid rgb(227, 227, 227); box-sizing: border-box; margin-top: 0px; margin-bottom: 0px;\"><br/></p><p><strong>1.VIP0 Benefits</strong></p><p>Register to receive 10,000 RP</p><p>Complete 1 task daily</p><p>After 3 days, you can earn 40,000 RP</p><p><br/></p>', '', '', '', '<p><strong>1.Manfaat VIP0</strong></p><p>&nbsp;&nbsp;&nbsp;&nbsp;Daftar untuk menerima 10.000 RP</p><p>&nbsp;&nbsp;&nbsp;&nbsp;Selesaikan 1 tugas setiap hari</p><p>&nbsp;&nbsp;&nbsp;&nbsp;Setelah 3 hari, Anda dapat memperoleh 40.000 RP</p><p><br/></p>', '', '', '', '', ''),
(18, 'VIP1', 2, '200000.00', 1, 0, 0, 'VIP1', 'VIP1', 'VIP1', 'VIP1', 'VIP1', 'VIP1', 'VIP1', 'VIP1', 'VIP1', 'VIP1', 1, '0.0000', NULL, NULL, 1, '0', 0, '10.0000', '3.0000', '1.0000', '<p><span style=\"text-wrap-mode: nowrap;\">1.VIP1专属特权</span></p><p><span style=\"text-wrap-mode: nowrap;\">升级费用：RP 200,000.00</span></p><p><span style=\"text-wrap-mode: nowrap;\">会员有效期1个月</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><span style=\"text-wrap-mode: nowrap;\">2.每日任务限制</span></p><p><span style=\"text-wrap-mode: nowrap;\">每天最多可完成1个任务</span></p><p><span style=\"text-wrap-mode: nowrap;\">抓住更多赚钱机会，任务越多，收益越丰厚！</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><span style=\"text-wrap-mode: nowrap;\">3.每日任务佣金</span></p><p><span style=\"text-wrap-mode: nowrap;\">每个任务的佣金在RP 10000至RP 30000</span></p><p><span style=\"text-wrap-mode: nowrap;\">每月可获得佣金300000至900000</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><span style=\"text-wrap-mode: nowrap;\">4.下属推荐奖励</span></p><p><span style=\"text-wrap-mode: nowrap;\">1级下属推荐奖励10%</span></p><p><span style=\"text-wrap-mode: nowrap;\">2级下属推荐奖励3%</span></p><p><span style=\"text-wrap-mode: nowrap;\">3级下属推荐奖励1%</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><span style=\"text-wrap-mode: nowrap;\">5.下属订单佣金</span></p><p><span style=\"text-wrap-mode: nowrap;\">1级下属订单返佣5%</span></p><p><span style=\"text-wrap-mode: nowrap;\">2级下属订单返佣2%</span></p><p><span style=\"text-wrap-mode: nowrap;\">3级下属订单返佣1%</span></p><p><br/></p>', '<p><strong><span style=\"text-wrap-mode: nowrap;\">1.VIP1 Exclusive Privileges</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Upgrade Fee: RP 200,000.00</span></p><p><span style=\"text-wrap-mode: nowrap;\">Membership Validity: 1 Month</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">2.Daily Task Limit</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">You can complete up to 1 task each day.</span></p><p><span style=\"text-wrap-mode: nowrap;\">Seize more opportunities to earn; the more tasks you complete, the greater your rewards!</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">3.Daily Task Commission</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">The commission for each task ranges from RP 10,000 to RP 30,000.</span></p><p><span style=\"text-wrap-mode: nowrap;\">You can earn a monthly commission of RP 300,000 to RP 900,000.</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">4.Subordinate Referral Rewards</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">1st Level Subordinate Referral Reward: 10%</span></p><p><span style=\"text-wrap-mode: nowrap;\">2nd Level Subordinate Referral Reward: 3%</span></p><p><span style=\"text-wrap-mode: nowrap;\">3rd Level Subordinate Referral Reward: 1%</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">5.Subordinate Order Commissions</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">1st Level Subordinate Order Rebate: 5%</span></p><p><span style=\"text-wrap-mode: nowrap;\">2nd Level Subordinate Order Rebate: 2%</span></p><p><span style=\"text-wrap-mode: nowrap;\">3rd Level Subordinate Order Rebate: 1%</span></p><p><br/></p>', '', '', '', '<p><strong><span style=\"text-wrap-mode: nowrap;\">1.Privilege Eksklusif VIP1</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Biaya Upgrade: RP 200.000,00</span></p><p><span style=\"text-wrap-mode: nowrap;\">Masa Berlaku Keanggotaan: 1 Bulan</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">2.Batas Tugas Harian</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Anda dapat menyelesaikan hingga 1 tugas setiap hari.</span></p><p><span style=\"text-wrap-mode: nowrap;\">Ambil lebih banyak kesempatan untuk menghasilkan; semakin banyak tugas yang Anda selesaikan, semakin besar imbalan Anda!</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">3.Komisi Tugas Harian</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Komisi untuk setiap tugas berkisar antara RP 10.000 hingga RP 30.000.</span></p><p><span style=\"text-wrap-mode: nowrap;\">Anda dapat memperoleh komisi bulanan antara RP 300.000 hingga RP 900.000.</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">4.Hadiah Rujukan Bawahan</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Hadiah Rujukan Bawahan Tingkat 1: 10%</span></p><p><span style=\"text-wrap-mode: nowrap;\">Hadiah Rujukan Bawahan Tingkat 2: 3%</span></p><p><span style=\"text-wrap-mode: nowrap;\">Hadiah Rujukan Bawahan Tingkat 3: 1%</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">5.Komisi Pesanan Bawahan</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Rebate Pesanan Bawahan Tingkat 1: 5%</span></p><p><span style=\"text-wrap-mode: nowrap;\">Rebate Pesanan Bawahan Tingkat 2: 2%</span></p><p><span style=\"text-wrap-mode: nowrap;\">Rebate Pesanan Bawahan Tingkat 3: 1%</span></p><p><br/></p>', '', '', '', '', ''),
(19, 'VIP2', 3, '800000.00', 1, 0, 0, 'VIP2', 'VIP2', 'VIP2', 'VIP2', 'VIP2', 'VIP2', 'VIP2', 'VIP2', 'VIP2', 'VIP2', 1, '0.0000', NULL, NULL, 1, '0', 0, '10.0000', '3.0000', '1.0000', '<p><span style=\"text-wrap-mode: nowrap;\">1.VIP2专属特权</span></p><p><span style=\"text-wrap-mode: nowrap;\">升级费用：RP 800,000.00</span></p><p><span style=\"text-wrap-mode: nowrap;\">会员有效期1个月</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><span style=\"text-wrap-mode: nowrap;\">2.每日任务限制</span></p><p><span style=\"text-wrap-mode: nowrap;\">每天最多可完成1个任务</span></p><p><span style=\"text-wrap-mode: nowrap;\">抓住更多赚钱机会，任务越多，收益越丰厚！</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><span style=\"text-wrap-mode: nowrap;\">3.每日任务佣金</span></p><p><span style=\"text-wrap-mode: nowrap;\">每个任务的佣金在RP 40000至RP 120000</span></p><p><span style=\"text-wrap-mode: nowrap;\">每月可获得佣金1200000至3600000</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><span style=\"text-wrap-mode: nowrap;\">4.下属推荐奖励</span></p><p><span style=\"text-wrap-mode: nowrap;\">1级下属推荐奖励10%</span></p><p><span style=\"text-wrap-mode: nowrap;\">2级下属推荐奖励3%</span></p><p><span style=\"text-wrap-mode: nowrap;\">3级下属推荐奖励1%</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><span style=\"text-wrap-mode: nowrap;\">5.下属订单佣金</span></p><p><span style=\"text-wrap-mode: nowrap;\">1级下属订单返佣5%</span></p><p><span style=\"text-wrap-mode: nowrap;\">2级下属订单返佣2%</span></p><p><span style=\"text-wrap-mode: nowrap;\">3级下属订单返佣1%</span></p><p><br/></p>', '<p><strong><span style=\"text-wrap-mode: nowrap;\">1.VIP2 Exclusive Privileges</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Upgrade Fee: RP 800,000.00</span></p><p><span style=\"text-wrap-mode: nowrap;\">Membership Validity: 1 Month</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">2.Daily Task Limit</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">You can complete up to 1 task each day.</span></p><p><span style=\"text-wrap-mode: nowrap;\">Seize more opportunities to earn; the more tasks you complete, the greater your rewards!</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">3.Daily Task Commission</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">The commission for each task ranges from RP 40,000 to RP 120,000.</span></p><p><span style=\"text-wrap-mode: nowrap;\">You can earn a monthly commission of RP 1,200,000 to RP 3,600,000.</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">4.Subordinate Referral Rewards</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">1st Level Subordinate Referral Reward: 10%</span></p><p><span style=\"text-wrap-mode: nowrap;\">2nd Level Subordinate Referral Reward: 3%</span></p><p><span style=\"text-wrap-mode: nowrap;\">3rd Level Subordinate Referral Reward: 1%</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">5.Subordinate Order Commissions</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">1st Level Subordinate Order Rebate: 5%</span></p><p><span style=\"text-wrap-mode: nowrap;\">2nd Level Subordinate Order Rebate: 2%</span></p><p><span style=\"text-wrap-mode: nowrap;\">3rd Level Subordinate Order Rebate: 1%</span></p><p><br/></p>', '', '', '', '<p><strong><span style=\"text-wrap-mode: nowrap;\">1.Privilege Eksklusif VIP2</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Biaya Upgrade: RP 800.000,00</span></p><p><span style=\"text-wrap-mode: nowrap;\">Masa Berlaku Keanggotaan: 1 Bulan</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">2.Batas Tugas Harian</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Anda dapat menyelesaikan hingga 1 tugas setiap hari.</span></p><p><span style=\"text-wrap-mode: nowrap;\">Ambil lebih banyak kesempatan untuk menghasilkan; semakin banyak tugas yang Anda selesaikan, semakin besar imbalan Anda!</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">3.Komisi Tugas Harian</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Komisi untuk setiap tugas berkisar antara RP 40.000 hingga RP 120.000.</span></p><p><span style=\"text-wrap-mode: nowrap;\">Anda dapat memperoleh komisi bulanan antara RP 1,200.000 hingga RP 3,600.000.</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">4.Hadiah Rujukan Bawahan</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Hadiah Rujukan Bawahan Tingkat 1: 10%</span></p><p><span style=\"text-wrap-mode: nowrap;\">Hadiah Rujukan Bawahan Tingkat 2: 3%</span></p><p><span style=\"text-wrap-mode: nowrap;\">Hadiah Rujukan Bawahan Tingkat 3: 1%</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">5.Komisi Pesanan Bawahan</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Rebate Pesanan Bawahan Tingkat 1: 5%</span></p><p><span style=\"text-wrap-mode: nowrap;\">Rebate Pesanan Bawahan Tingkat 2: 2%</span></p><p><span style=\"text-wrap-mode: nowrap;\">Rebate Pesanan Bawahan Tingkat 3: 1%</span></p><p><br/></p>', '', '', '', '', ''),
(20, 'VIP3', 4, '2500000.00', 1, 0, 0, 'VIP3', 'VIP3', 'VIP3', 'VIP3', 'VIP3', 'VIP3', 'VIP3', 'VIP3', 'VIP3', 'VIP3', 1, '0.0000', NULL, NULL, 1, '0', 0, '10.0000', '3.0000', '1.0000', '<p><span style=\"text-wrap-mode: nowrap;\">1.VIP3专属特权</span></p><p><span style=\"text-wrap-mode: nowrap;\">升级费用：RP 2,500,000.00</span></p><p><span style=\"text-wrap-mode: nowrap;\">会员有效期1个月</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><span style=\"text-wrap-mode: nowrap;\">2.每日任务限制</span></p><p><span style=\"text-wrap-mode: nowrap;\">每天最多可完成1个任务</span></p><p><span style=\"text-wrap-mode: nowrap;\">抓住更多赚钱机会，任务越多，收益越丰厚！</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><span style=\"text-wrap-mode: nowrap;\">3.每日任务佣金</span></p><p><span style=\"text-wrap-mode: nowrap;\">每个任务的佣金在RP 125000至RP 300000</span></p><p><span style=\"text-wrap-mode: nowrap;\">每月可获得佣金3750000至9000000</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><span style=\"text-wrap-mode: nowrap;\">4.下属推荐奖励</span></p><p><span style=\"text-wrap-mode: nowrap;\">1级下属推荐奖励10%</span></p><p><span style=\"text-wrap-mode: nowrap;\">2级下属推荐奖励3%</span></p><p><span style=\"text-wrap-mode: nowrap;\">3级下属推荐奖励1%</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><span style=\"text-wrap-mode: nowrap;\">5.下属订单佣金</span></p><p><span style=\"text-wrap-mode: nowrap;\">1级下属订单返佣5%</span></p><p><span style=\"text-wrap-mode: nowrap;\">2级下属订单返佣2%</span></p><p><span style=\"text-wrap-mode: nowrap;\">3级下属订单返佣1%</span></p><p><br/></p>', '<p><strong><span style=\"text-wrap-mode: nowrap;\">1.VIP3 Exclusive Privileges</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Upgrade Fee: RP 2,500,000.00</span></p><p><span style=\"text-wrap-mode: nowrap;\">Membership Validity: 1 Month</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">2.Daily Task Limit</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">You can complete up to 1 task each day.</span></p><p><span style=\"text-wrap-mode: nowrap;\">Seize more opportunities to earn; the more tasks you complete, the greater your rewards!</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">3.Daily Task Commission</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">The commission for each task ranges from RP 125,000 to RP 300,000.</span></p><p><span style=\"text-wrap-mode: nowrap;\">You can earn a monthly commission of RP 3,750,000 to RP 9,000,000</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">4.Subordinate Referral Rewards</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">1st Level Subordinate Referral Reward: 10%</span></p><p><span style=\"text-wrap-mode: nowrap;\">2nd Level Subordinate Referral Reward: 3%</span></p><p><span style=\"text-wrap-mode: nowrap;\">3rd Level Subordinate Referral Reward: 1%</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">5.Subordinate Order Commissions</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">1st Level Subordinate Order Rebate: 5%</span></p><p><span style=\"text-wrap-mode: nowrap;\">2nd Level Subordinate Order Rebate: 2%</span></p><p><span style=\"text-wrap-mode: nowrap;\">3rd Level Subordinate Order Rebate: 1%</span></p><p><br/></p>', '', '', '', '<p><strong><span style=\"text-wrap-mode: nowrap;\">1.Privilege Eksklusif VIP3</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Biaya Upgrade: RP 2,500.000,00</span></p><p><span style=\"text-wrap-mode: nowrap;\">Masa Berlaku Keanggotaan: 1 Bulan</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">2.Batas Tugas Harian</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Anda dapat menyelesaikan hingga 1 tugas setiap hari.</span></p><p><span style=\"text-wrap-mode: nowrap;\">Ambil lebih banyak kesempatan untuk menghasilkan; semakin banyak tugas yang Anda selesaikan, semakin besar imbalan Anda!</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">3.Komisi Tugas Harian</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Komisi untuk setiap tugas berkisar antara RP 125,000 hingga RP 300,000</span></p><p><span style=\"text-wrap-mode: nowrap;\">Anda dapat memperoleh komisi bulanan antara RP 3,750,000 hingga RP 9,000,000</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">4.Hadiah Rujukan Bawahan</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Hadiah Rujukan Bawahan Tingkat 1: 10%</span></p><p><span style=\"text-wrap-mode: nowrap;\">Hadiah Rujukan Bawahan Tingkat 2: 3%</span></p><p><span style=\"text-wrap-mode: nowrap;\">Hadiah Rujukan Bawahan Tingkat 3: 1%</span></p><p><span style=\"text-wrap-mode: nowrap;\"><br/></span></p><p><strong><span style=\"text-wrap-mode: nowrap;\">5.Komisi Pesanan Bawahan</span></strong></p><p><span style=\"text-wrap-mode: nowrap;\">Rebate Pesanan Bawahan Tingkat 1: 5%</span></p><p><span style=\"text-wrap-mode: nowrap;\">Rebate Pesanan Bawahan Tingkat 2: 2%</span></p><p><span style=\"text-wrap-mode: nowrap;\">Rebate Pesanan Bawahan Tingkat 3: 1%</span></p><p><br/></p>', '', '', '', '', ''),
(21, 'VIP4', 5, '50000000.00', 1, 0, 0, 'VIP4', 'VIP4', 'VIP4', 'VIP4', 'VIP4', 'VIP4', 'VIP4', 'VIP4', 'VIP4', 'VIP4', 1, '0.0000', NULL, NULL, 1, '0', 0, '5.0000', '2.0000', '1.0000', '', '', '', '', '', '', '', '', '', '', ''),
(22, 'VIP5', 6, '100000000.00', 1, 0, 0, 'VIP5', 'VIP5', 'VIP5', 'VIP5', 'VIP5', 'VIP5', 'VIP5', 'VIP5', 'VIP5', 'VIP5', 1, '0.0000', NULL, NULL, 1, '0', 0, '5.0000', '2.0000', '1.0000', '', '', '', '', '', '', '', '', '', '', ''),
(23, 'VIP6', 7, '200000000.00', 1, 0, 0, 'VIP6', 'VIP6', 'VIP6', 'VIP6', 'VIP6', 'VIP6', 'VIP6', 'VIP6', 'VIP6', 'VIP6', 1, '0.0000', NULL, NULL, 1, '0', 1, '5.0000', '2.0000', '1.0000', '', '', '', '', '', '', '', '', '', '', '');
/*!40000 ALTER TABLE `ly_user_grade` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_user_message`
--

DROP TABLE IF EXISTS `ly_user_message`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_user_message` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '留言者uid',
  `message` text NOT NULL DEFAULT '' COMMENT '留言内容',
  `time` int(11) NOT NULL DEFAULT '0' COMMENT '留言时间',
  `mName` varchar(30) NOT NULL DEFAULT '' COMMENT '留言者姓名',
  `mPhone` char(11) NOT NULL DEFAULT '' COMMENT '留言者电话',
  `mMail` varchar(100) DEFAULT NULL COMMENT '留言者邮箱',
  `mAddress` varchar(255) DEFAULT NULL COMMENT '留言者地址',
  `picture_url` text COMMENT '图片地址',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '留言类型。1=留言，2=评论',
  `message_id` int(11) NOT NULL DEFAULT '0' COMMENT '评论的从属留言id',
  `is_show` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否显示.1=显示,2=不显示',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_user_message`
--

LOCK TABLES `ly_user_message` WRITE;
/*!40000 ALTER TABLE `ly_user_message` DISABLE KEYS */;
/*!40000 ALTER TABLE `ly_user_message` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_user_recharge`
--

DROP TABLE IF EXISTS `ly_user_recharge`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_user_recharge` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `order_number` char(50) NOT NULL DEFAULT '' COMMENT '订单号',
  `type` int(11) NOT NULL DEFAULT '0' COMMENT '充值方式（见充值方式表ID）',
  `money` decimal(16,4) NOT NULL DEFAULT '0.0000' COMMENT '充值金额',
  `daozhang_money` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '到账金额,0表示到账金额为money中的字段',
  `rid` int(11) NOT NULL DEFAULT '0' COMMENT '收款账号ID',
  `bid` int(11) NOT NULL DEFAULT '0' COMMENT '银行ID',
  `postscript` varchar(255) NOT NULL DEFAULT '' COMMENT '附言',
  `state` tinyint(4) NOT NULL DEFAULT '3' COMMENT '状态。\r\n1：成功；\r\n2：失败；\r\n3：审核中；',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '提交时间',
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '客服ID',
  `dispose_time` int(11) NOT NULL DEFAULT '0' COMMENT '处理时间',
  `remarks` text NOT NULL DEFAULT '' COMMENT '客服说明，升级VIP说明',
  `fee` decimal(16,4) NOT NULL DEFAULT '0.0000' COMMENT '手续费',
  `first_recharge` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否已经领取首充奖励',
  `is_update_level` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否升级VIP等级。1是，2否',
  `account_receivable` varchar(50) NOT NULL DEFAULT '' COMMENT '收款账号',
  `bank_code` varchar(50) NOT NULL DEFAULT '' COMMENT '银行代码',
  `screenshots` text,
  `submitUrl` varchar(500) NOT NULL DEFAULT '' COMMENT '支付链接URL',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `order_number` (`order_number`) USING BTREE,
  KEY `uid` (`uid`) USING BTREE,
  KEY `type` (`type`) USING BTREE,
  KEY `state` (`state`) USING BTREE,
  KEY `add_time` (`add_time`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户充值';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_user_recharge`
--

LOCK TABLES `ly_user_recharge` WRITE;
/*!40000 ALTER TABLE `ly_user_recharge` DISABLE KEYS */;
/*!40000 ALTER TABLE `ly_user_recharge` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_user_signin`
--

DROP TABLE IF EXISTS `ly_user_signin`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_user_signin` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) DEFAULT NULL,
  `times` int(11) DEFAULT NULL COMMENT '签到时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 ROW_FORMAT=FIXED COMMENT='用户签到';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_user_signin`
--

LOCK TABLES `ly_user_signin` WRITE;
/*!40000 ALTER TABLE `ly_user_signin` DISABLE KEYS */;
INSERT INTO `ly_user_signin` VALUES (1,48,1653580800),(2,20,1653580800),(3,56,1653580800),(4,54,1653580800),(5,61,1728835200);
/*!40000 ALTER TABLE `ly_user_signin` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_user_task`
--

DROP TABLE IF EXISTS `ly_user_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_user_task` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `task_id` int(11) NOT NULL DEFAULT '0' COMMENT '发布任务的id',
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '购买人的uid',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '任务的状态。1：进行中；2：审核中；3：已完成；4：已失败;5:恶意',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '购买任务的时间',
  `username` varchar(50) NOT NULL DEFAULT '' COMMENT '购买人的username',
  `fuid` int(11) NOT NULL DEFAULT '0' COMMENT '发布人uid',
  `trial_time` int(11) NOT NULL DEFAULT '0' COMMENT '提交审核时间',
  `handle_time` int(11) NOT NULL DEFAULT '0' COMMENT '处理时间',
  `examine_demo` text NOT NULL COMMENT '审核样例',
  `trial_remarks` text NOT NULL COMMENT '提交审核说明',
  `handle_remarks` text NOT NULL COMMENT '处理说明',
  `complete_time` int(11) NOT NULL DEFAULT '0' COMMENT '完成时间',
  `auto_audit_time` int(11) NOT NULL DEFAULT '0' COMMENT '自动审核时间戳，0表示不需要自动审核，大于0表示到达该时间戳时自动审核',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `task_id` (`task_id`) USING BTREE,
  KEY `uid` (`uid`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `add_time` (`add_time`) USING BTREE,
  KEY `trial_time` (`trial_time`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户领取的任务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_user_task`
--

LOCK TABLES `ly_user_task` WRITE;
/*!40000 ALTER TABLE `ly_user_task` DISABLE KEYS */;
/*!40000 ALTER TABLE `ly_user_task` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_user_team`
--

DROP TABLE IF EXISTS `ly_user_team`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_user_team` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `team` int(11) NOT NULL DEFAULT '0' COMMENT '团队成员',
  `lock` tinyint(4) DEFAULT '2' COMMENT '1:锁定 2:未锁定',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uid_team` (`uid`,`team`) USING BTREE,
  KEY `uid` (`uid`) USING BTREE,
  KEY `team` (`team`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=FIXED COMMENT='用户团队表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_user_team`
--

LOCK TABLES `ly_user_team` WRITE;
/*!40000 ALTER TABLE `ly_user_team` DISABLE KEYS */;
INSERT INTO `ly_user_team` VALUES (1,100,100,2);
/*!40000 ALTER TABLE `ly_user_team` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_user_total`
--

DROP TABLE IF EXISTS `ly_user_total`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_user_total` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `total_recharge` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '充值总数',
  `total_withdrawals` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '提现总数',
  `balance_investment` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '订单总数',
  `total_rebate` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '返点总数',
  `total_bonus` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '分红总数',
  `total_activity` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '活动总数',
  `balance` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '用户余额',
  `total_balance` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '账户总额',
  `total_fee` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '费用',
  `total_buybi` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '买币',
  `total_sellbi` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '卖币',
  `total_robbi` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '抢币',
  `total_commission` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '佣金',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uid` (`uid`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=1149 DEFAULT CHARSET=utf8 ROW_FORMAT=FIXED COMMENT='用户数据统计表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_user_total`
--

LOCK TABLES `ly_user_total` WRITE;
/*!40000 ALTER TABLE `ly_user_total` DISABLE KEYS */;
INSERT INTO `ly_user_total` VALUES (100,100,0.00,0.00,0.00,0.00,0.00,0.00,0.84,0.00,0.00,0.00,0.00,0.00,0.00);
/*!40000 ALTER TABLE `ly_user_total` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_user_vip`
--

DROP TABLE IF EXISTS `ly_user_vip`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_user_vip` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL DEFAULT '' COMMENT '名称用户',
  `uid` int(11) NOT NULL DEFAULT '0',
  `state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1正常3过期',
  `en_name` varchar(25) DEFAULT NULL,
  `name` varchar(25) DEFAULT NULL COMMENT '可领取任务次数',
  `ft_name` varchar(255) NOT NULL DEFAULT '',
  `ry_name` varchar(255) NOT NULL DEFAULT '',
  `ydn_name` varchar(255) NOT NULL DEFAULT '',
  `xby_name` varchar(255) NOT NULL DEFAULT '',
  `yn_name` varchar(255) NOT NULL DEFAULT '',
  `ty_name` varchar(255) NOT NULL DEFAULT '',
  `yd_name` varchar(255) NOT NULL DEFAULT '',
  `ma_name` varchar(255) NOT NULL DEFAULT '',
  `pt_name` varchar(50) NOT NULL DEFAULT '',
  `grade` int(11) DEFAULT NULL,
  `stime` int(11) DEFAULT NULL COMMENT 'VIP开始时间',
  `etime` int(11) DEFAULT NULL COMMENT 'VIP结束时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `uid` (`uid`) USING BTREE,
  KEY `state` (`state`) USING BTREE,
  KEY `etime` (`etime`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=754 DEFAULT CHARSET=utf8 ROW_FORMAT=FIXED COMMENT='用户团队表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_user_vip`
--

LOCK TABLES `ly_user_vip` WRITE;
/*!40000 ALTER TABLE `ly_user_vip` DISABLE KEYS */;
INSERT INTO `ly_user_vip` VALUES (79,'22',100,1,'Silver member','白银会员','','','','','','','','','',2,**********,**********);
/*!40000 ALTER TABLE `ly_user_vip` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_user_withdrawals`
--

DROP TABLE IF EXISTS `ly_user_withdrawals`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_user_withdrawals` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `order_number` char(25) NOT NULL DEFAULT '' COMMENT '订单号',
  `bank_id` char(25) NOT NULL DEFAULT '' COMMENT '提现类型',
  `bank_name` varchar(50) NOT NULL DEFAULT '' COMMENT '银行名称',
  `card_number` char(200) NOT NULL DEFAULT '' COMMENT '提现账号',
  `card_name` char(20) NOT NULL DEFAULT '' COMMENT '户名',
  `price` decimal(16,4) NOT NULL DEFAULT '0.0000' COMMENT '提现金额',
  `time` int(11) NOT NULL DEFAULT '0' COMMENT '提现时间',
  `trade_number` char(25) NOT NULL DEFAULT '' COMMENT '流水号',
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '财务ID',
  `channel_id` int(11) NOT NULL DEFAULT '0' COMMENT '代付渠道ID',
  `process_time` int(11) NOT NULL DEFAULT '0' COMMENT '代付处理时间',
  `success_time` int(11) NOT NULL DEFAULT '0' COMMENT '代付成功时间',
  `fail_time` int(11) NOT NULL DEFAULT '0' COMMENT '代付失败时间',
  `fail_reason` varchar(255) NOT NULL DEFAULT '' COMMENT '代付失败原因',
  `examine` tinyint(4) NOT NULL DEFAULT '3' COMMENT '风控是否审核。\r\n１：审核通过；\r\n２：审核未通过；\r\n３：未审核；',
  `state` tinyint(4) NOT NULL DEFAULT '3' COMMENT '状态。\r\n1；成功；\r\n2：失败；\r\n3：审核中；',
  `remarks` text NOT NULL DEFAULT '' COMMENT '备注',
  `set_time` int(11) NOT NULL DEFAULT '0' COMMENT '处理时间',
  `fee` decimal(16,4) NOT NULL DEFAULT '0.0000',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `order_number` (`order_number`) USING BTREE,
  KEY `uid` (`uid`) USING BTREE,
  KEY `state` (`state`) USING BTREE,
  KEY `time` (`time`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=284 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户提现表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_user_withdrawals`
--

LOCK TABLES `ly_user_withdrawals` WRITE;
/*!40000 ALTER TABLE `ly_user_withdrawals` DISABLE KEYS */;
/*!40000 ALTER TABLE `ly_user_withdrawals` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_users`
--

DROP TABLE IF EXISTS `ly_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(11) NOT NULL DEFAULT '',
  `sid` int(11) NOT NULL DEFAULT '0' COMMENT '上级ID',
  `identity_id` varchar(30) NOT NULL DEFAULT '' COMMENT '身份证号',
  `realname` varchar(30) NOT NULL DEFAULT '' COMMENT '实名',
  `username` char(30) NOT NULL DEFAULT '' COMMENT '用户名',
  `password` char(255) NOT NULL DEFAULT '' COMMENT '密码',
  `nickname` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_polish_ci NOT NULL DEFAULT '' COMMENT '昵称',
  `openid` varchar(255) NOT NULL DEFAULT '' COMMENT '微信OPENID',
  `fund_password` char(255) NOT NULL DEFAULT '' COMMENT '资金密码',
  `rebate` decimal(8,4) NOT NULL DEFAULT '0.0000' COMMENT '返点',
  `vip_level` smallint(6) NOT NULL DEFAULT '1' COMMENT 'VIP等级',
  `user_type` tinyint(4) NOT NULL DEFAULT '2' COMMENT '用户类型。\r\n1：代理；\r\n2：会员；\r\n3:测试;',
  `reg_time` int(11) NOT NULL DEFAULT '0' COMMENT '注册时间',
  `login_error` tinyint(4) NOT NULL DEFAULT '0' COMMENT '登录错误次数',
  `error_interval` int(11) NOT NULL DEFAULT '0' COMMENT '登录错误上限后间隔时间',
  `islock` tinyint(4) NOT NULL DEFAULT '2' COMMENT '银行卡信息锁定。\r\n1：锁定；\r\n2：未锁定；\r\n',
  `iswage` tinyint(4) NOT NULL DEFAULT '2' COMMENT '是否开启工资，\r\n1：开启；\r\n2：未开启；\r\n',
  `losswage` tinyint(4) NOT NULL DEFAULT '2' COMMENT '是否开启亏损分红。\r\n1：是；\r\n2：否；',
  `isbonus` tinyint(4) NOT NULL DEFAULT '2' COMMENT '是否开启分红。\r\n1：开启；\r\n2：关闭；',
  `state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '用户状态。\r\n1：正常；\r\n2：禁止登录；',
  `recharge_state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '充值状态。\r\n1：开启；\r\n2：关闭；',
  `withdrawals_state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '提现状态。\r\n1：开启；\r\n2：关闭；',
  `transfer` tinyint(4) NOT NULL DEFAULT '2' COMMENT '下级转账。\r\n1：开启；\r\n2：关闭；',
  `last_login` int(11) NOT NULL DEFAULT '0' COMMENT '上次登录时间',
  `last_ip` char(15) NOT NULL DEFAULT '' COMMENT '上次登录IP',
  `phone` char(30) NOT NULL DEFAULT '' COMMENT '电话（手机）号码',
  `qq` char(15) NOT NULL DEFAULT '' COMMENT 'QQ号码',
  `alipay` char(30) NOT NULL DEFAULT '' COMMENT '支付宝',
  `greet` varchar(60) NOT NULL DEFAULT '' COMMENT '问候语',
  `question` varchar(60) NOT NULL DEFAULT '' COMMENT '安全问题',
  `answer` varchar(60) NOT NULL DEFAULT '' COMMENT '安全问题答案',
  `login_number` int(11) unsigned zerofill NOT NULL DEFAULT '00000000000' COMMENT '总登录次数',
  `danger` tinyint(4) NOT NULL DEFAULT '2' COMMENT '是否是风险帐号。\r\n1：是；\r\n2：否；',
  `header` varchar(100) NOT NULL DEFAULT '' COMMENT '头像',
  `isuppw` tinyint(4) NOT NULL DEFAULT '0' COMMENT '找回密码状态',
  `mail` varchar(100) NOT NULL DEFAULT '' COMMENT '邮箱',
  `sex` varchar(10) NOT NULL DEFAULT '保密' COMMENT '性别',
  `grade` tinyint(4) NOT NULL DEFAULT '1' COMMENT '等级',
  `experience` int(11) NOT NULL DEFAULT '0' COMMENT '积分经验',
  `last_grade` int(11) NOT NULL DEFAULT '1' COMMENT '升级前等级',
  `grade_reward_price` decimal(16,4) NOT NULL DEFAULT '0.0000' COMMENT '升级奖励金额 ',
  `birthday` varchar(60) NOT NULL DEFAULT '' COMMENT '生日',
  `idcode` varchar(11) NOT NULL DEFAULT '0' COMMENT '邀请码',
  `regurl` varchar(255) NOT NULL DEFAULT '' COMMENT '注册链接',
  `regqrurl` varchar(255) NOT NULL DEFAULT '' COMMENT '二维码注册链接',
  `regnum` int(11) NOT NULL DEFAULT '0' COMMENT '使用邀请码注册次数',
  `dest` varchar(60) NOT NULL DEFAULT '' COMMENT '国家区号',
  `at_time` int(11) NOT NULL DEFAULT '0' COMMENT '激活时间',
  `is_admin` tinyint(4) NOT NULL DEFAULT '2' COMMENT '是否是码商.\r\n1:是;\r\n2:否;',
  `idcard` varchar(50) NOT NULL DEFAULT '' COMMENT '身份证\n\n',
  `weixin` varchar(75) NOT NULL DEFAULT '' COMMENT '微信号',
  `credit` int(11) NOT NULL DEFAULT '60' COMMENT '信用',
  `recommend` varchar(25) NOT NULL DEFAULT '' COMMENT '推荐人 邀请码',
  `douyin` varchar(255) NOT NULL DEFAULT '' COMMENT '抖音号',
  `kuaishou` varchar(255) NOT NULL DEFAULT '' COMMENT '快手号',
  `alipay_name` varchar(75) NOT NULL DEFAULT '' COMMENT '支付宝名称',
  `alipay_names` varchar(255) NOT NULL DEFAULT '',
  `is_housekeeper` tinyint(4) DEFAULT '0' COMMENT '云管家',
  `housekeeper_time` int(11) DEFAULT NULL COMMENT '过期时间',
  `is_spread` tinyint(4) DEFAULT '0' COMMENT '是否已经返佣',
  `s_hb` tinyint(4) DEFAULT '2' COMMENT '是否私法红包1可以2不行',
  `is_auto_f` tinyint(4) DEFAULT '2' COMMENT '是否允许下级自动加密聊好友',
  `zcip` char(15) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `id` (`id`) USING BTREE,
  KEY `sid` (`sid`) USING BTREE,
  KEY `state` (`state`) USING BTREE,
  KEY `vip_level` (`vip_level`) USING BTREE,
  KEY `reg_time` (`reg_time`) USING BTREE,
  KEY `uid` (`uid`) USING BTREE
) ENGINE=MyISAM AUTO_INCREMENT=1149 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_users`
--

LOCK TABLES `ly_users` WRITE;
/*!40000 ALTER TABLE `ly_users` DISABLE KEYS */;
INSERT INTO `ly_users` (`id`, `uid`, `sid`, `identity_id`, `realname`, `username`, `password`, `nickname`, `openid`, `fund_password`, `rebate`, `vip_level`, `user_type`, `reg_time`, `login_error`, `error_interval`, `islock`, `iswage`, `losswage`, `isbonus`, `state`, `recharge_state`, `withdrawals_state`, `transfer`, `last_login`, `last_ip`, `phone`, `qq`, `alipay`, `greet`, `question`, `answer`, `login_number`, `danger`, `header`, `isuppw`, `mail`, `sex`, `grade`, `experience`, `last_grade`, `grade_reward_price`, `birthday`, `idcode`, `regurl`, `regqrurl`, `regnum`, `dest`, `at_time`, `is_admin`, `idcard`, `weixin`, `credit`, `recommend`, `douyin`, `kuaishou`, `alipay_name`, `alipay_names`, `is_housekeeper`, `housekeeper_time`, `is_spread`, `s_hb`, `is_auto_f`, `zcip`) VALUES
(100, '6687909', 0, '', 'sixuooasx', '22', '1ec3FwgjPvGsKa69+JxOYQRu68cAQG3xwUxYzV1Nla1ChDYQcYY', '', '', '06d8XDnTaZWIozq/n5lDYxk/ElxNEV1POmtq9IVEIJLJVcs', 10.00, 2, 1, 1730106687, 0, 0, 2, 2, 2, 2, 1, 1, 1, 2, 1734170746, '**************', '22', '', '', '', '', '', 00000000064, 2, 'head_1.png', 0, '', '保密', 1, 0, 1, 0.0000, '', '6687909', '', '', 0, '', 0, 2, '', '', 60, '', '', '', '', '', 0, NULL, 0, 2, 2, '');
/*!40000 ALTER TABLE `ly_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_wheel`
--

DROP TABLE IF EXISTS `ly_wheel`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_wheel` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(25) DEFAULT NULL,
  `name_hk` varchar(50) DEFAULT NULL,
  `name_id` varchar(50) DEFAULT NULL,
  `name_cn` varchar(255) DEFAULT NULL,
  `name_th` varchar(255) DEFAULT NULL,
  `name_yd` varchar(255) DEFAULT NULL,
  `name_vn` varchar(255) DEFAULT NULL,
  `name_jp` varchar(255) DEFAULT NULL,
  `name_es` varchar(255) DEFAULT NULL,
  `rate` varchar(10) DEFAULT NULL,
  `time` int(11) NOT NULL DEFAULT '0',
  `num` int(11) NOT NULL DEFAULT '0',
  `type` int(11) NOT NULL DEFAULT '0' COMMENT '奖品类型：0=谢谢参与，1=任务次数，2=现金奖励',
  `order` int(2) NOT NULL DEFAULT '9',
  `name_ma` varchar(255) NOT NULL DEFAULT '',
  `name_pt` varchar(255) NOT NULL DEFAULT '',
  `cash_amount` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '现金奖励金额',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=11 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_wheel`
--

LOCK TABLES `ly_wheel` WRITE;
/*!40000 ALTER TABLE `ly_wheel` DISABLE KEYS */;
INSERT INTO `ly_wheel` VALUES
(1,'get 2 more task','任務次數2次','Tugas plus 2','任务次数2次','จำนวนงาน 2 ครั้ง','कार्यों की संख्या: 2','Số lần thử lại:2','タスク回数2回','Número de tareas 2','15',1592537095,2,1,1,'Bilangan tugas: 2','Número de tarefas: 2',0.00),
(2,'Thank you','謝謝參與','Terima kasih','谢谢参与','ขอบคุณสำหรับการมีส่วนร่วม','भाग लेने के लिए धन्यवाद','Cảm ơn bạn đã tham gia','ご参加ありがとうございます','Gracias por participar','30',1592537137,0,0,2,'Terima kasih kerana mengambil bahagian','Obrigado por participar',0.00),
(3,'get 1 more task','任務次數1次','Tugas plus 1','任务次数1次','จำนวนงาน 1 ครั้ง','कार्यों की संख्या: 1','Số lần thử lại:1','タスク回数1回','Número de tareas 1','20',1592537273,1,1,3,'Bilangan tugas: 1','Número de tarefas: 1',0.00),
(4,'get 3 more task','任務次數3次','Tugas plus 3','任务次数3次','จำนวนงาน 3 ครั้ง','कार्यों की संख्या: 3','Số lần thử lại:3','タスク回数3回','Número de tareas 3','10',1592537273,3,1,4,'Bilangan tugas: 3','Número de tarefas: 3',0.00),
(5,'Cash $1','現金$1','Uang tunai $1','现金$1','เงินสด $1','नकद $1','Tiền mặt $1','現金$1','Efectivo $1','5',1592537273,0,2,5,'Wang tunai $1','Dinheiro $1',1.00),
(6,'get 5 more task','任務次數5次','Tugas plus 5','任务次数5次','จำนวนงาน 5 ครั้ง','कार्यों की संख्या: 5','Số lần thử lại:5','タスク回数5回','Número de tareas 5','8',1592537273,5,1,6,'Bilangan tugas: 5','Número de tarefas: 5',0.00),
(7,'Thank you','謝謝參與','Terima kasih','谢谢参与','ขอบคุณสำหรับการมีส่วนร่วม','भाग लेने के लिए धन्यवाद','Cảm ơn bạn đã tham gia','ご参加ありがとうございます','Gracias por participar','25',1592537273,0,0,7,'Terima kasih kerana mengambil bahagian','Obrigado por participar',0.00),
(8,'get 10 more task','任務次數10次','Tugas plus 10','任务次数10次','จำนวนงาน 10 ครั้ง','कार्यों की संख्या: 10','Số lần thử lại:10','タスク回数10回','Número de tareas 10','2',1592537273,10,1,8,'Bilangan tugas: 10','Número de tarefas: 10',0.00),
(9,'Cash $2','現金$2','Uang tunai $2','现金$2','เงินสด $2','नकद $2','Tiền mặt $2','現金$2','Efectivo $2','3',1592537273,0,2,9,'Wang tunai $2','Dinheiro $2',2.00);

/*!40000 ALTER TABLE `ly_wheel` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_wheel_config`
--

DROP TABLE IF EXISTS `ly_wheel_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_wheel_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `remark` text COMMENT '游戏说明',
  `remark_hk` text,
  `remark_id` text,
  `remark_cn` text,
  `remark_th` text,
  `remark_yd` text,
  `remark_vn` text,
  `remark_jp` text,
  `remark_es` text,
  `remark_pt` text NOT NULL DEFAULT '',
  `remark_ma` text NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_wheel_config`
--

LOCK TABLES `ly_wheel_config` WRITE;
/*!40000 ALTER TABLE `ly_wheel_config` DISABLE KEYS */;
INSERT INTO `ly_wheel_config` VALUES (
    1,
    '<p>1、If you have any questions, please ask for help from customer service</p><p>2、All interpretation rights belong to the company</p>',
    '<p>1、如有疑問請尋找客服幫助</p><p>2、壹切解釋權歸本公司所有</p>',
    '<p>1. Jika Anda memiliki pertanyaan, silakan mencari bantuan dari layanan pelanggan</p><p>2. Semua hak interpretasi adalah milik perusahaan kami</p>',
    '<p>1、如有疑问请寻找客服帮助</p><p>2、壹切解释权归本公司所有</p>',
    '<p>1. ถ้าคุณมีคำถามใดๆโปรดติดต่อเรา</p><p>2. สิทธิในการตีความทั้งหมดเป็นของบริษัทของเรา</p>',
    '<p>1. यदि आपके पास कोई प्रश्न है, कृपया ग्राहक सेवा मदद के लिए खोजें</p><p>2. सभी अनुवाद अधिकार कंपनी के साथ है</p>',
    '<p>1. Nếu có thắc mắc gì, xin hãy tìm trợ giúp dịch vụ khách hàng.</p><p>2. Tất cả quyền giải thích đều thuộc về công ty.</p>',
    '<p>1、質問があればカスタマーサービスのヘルプを探してください</p><p>2、壱切解釈権は当社の所有</p>',
    '<p>1. Si tiene alguna pregunta, por favor busque ayuda de servicio al cliente</p><p>2. Todos los derechos de interpretación son propiedad de la empresa</p>',
    '<p>1. se você tiver alguma dúvida, por favor, procure ajuda de atendimento ao cliente</p><p>2. Todos os direitos de interpretação pertencem à empresa</p>',
    '<p>1. Jika anda mempunyai sebarang soalan, sila cari bantuan perkhidmatan pelanggan</p><p>2. All interpretation rights belong to the company</p>'
);
/*!40000 ALTER TABLE `ly_wheel_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_wheel_record`
--

DROP TABLE IF EXISTS `ly_wheel_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_wheel_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT '0',
  `wheel_id` int(11) NOT NULL DEFAULT '0',
  `time` int(11) NOT NULL DEFAULT '0',
  `wheel_name` varchar(22) DEFAULT '奖品',
  `end_time` int(11) DEFAULT '0' COMMENT '过期时间',
  `num` int(11) DEFAULT '0' COMMENT '任务次数',
  `amount` int(11) DEFAULT '0',
  `cash_amount` decimal(16,2) DEFAULT '0.00' COMMENT '现金奖励金额',
  `prize_type` tinyint(4) DEFAULT '0' COMMENT '奖品类型：0=谢谢参与，1=任务次数，2=现金奖励',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=9 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_wheel_record`
--

LOCK TABLES `ly_wheel_record` WRITE;
/*!40000 ALTER TABLE `ly_wheel_record` DISABLE KEYS */;
INSERT INTO `ly_wheel_record` VALUES
(1, 48, 1, 1653626050, '任务次数2次', 1653712450, 2, 0, 0.00, 0),
(2, 48, 2, 1653629719, '任务次数2次', 1653716119, 2, 0, 0.00, 0),
(3, 48, 10, 1653629725, '任务次数5次', 1653716125, 5, 0, 0.00, 0),
(4, 54, 0, 1653631671, '谢谢参与',   0,          0, 0, 0.00, 0),
(5, 54, 2, 1653631677, '任务次数2次', 1653718077, 2, 0, 0.00, 0),
(6, 54, 0, 1653631686, '谢谢参与',   0,          0, 0, 0.00, 0),
(7, 20, 9, 1653645918, '任务次数5次', 1653732318, 5, 0, 0.00, 0),
(8, 20, 8, 1653645925, '任务次数10次',1653732325, 10,0, 0.00, 0);
/*!40000 ALTER TABLE `ly_wheel_record` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_user_lottery_times`
--

DROP TABLE IF EXISTS `ly_user_lottery_times`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_user_lottery_times` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `date` int(11) NOT NULL DEFAULT '0' COMMENT '日期（时间戳）',
  `base_times` int(11) NOT NULL DEFAULT '0' COMMENT 'VIP等级基础抽奖次数',
  `reward_times` int(11) NOT NULL DEFAULT '0' COMMENT '奖励获得的抽奖次数',
  `used_times` int(11) NOT NULL DEFAULT '0' COMMENT '已使用的抽奖次数',
  `remaining_times` int(11) NOT NULL DEFAULT '0' COMMENT '剩余抽奖次数',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uid_date` (`uid`,`date`),
  KEY `uid` (`uid`),
  KEY `date` (`date`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='用户每日抽奖次数记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_user_lottery_times`
--

LOCK TABLES `ly_user_lottery_times` WRITE;
/*!40000 ALTER TABLE `ly_user_lottery_times` DISABLE KEYS */;
/*!40000 ALTER TABLE `ly_user_lottery_times` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_user_lottery_log`
--

DROP TABLE IF EXISTS `ly_user_lottery_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_user_lottery_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT '0' COMMENT '获得奖励的用户ID',
  `buyer_uid` int(11) NOT NULL DEFAULT '0' COMMENT '购买VIP的用户ID',
  `lottery_times` int(11) NOT NULL DEFAULT '0' COMMENT '获得的抽奖次数',
  `purchase_type` varchar(20) NOT NULL DEFAULT '' COMMENT '购买类型',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`),
  KEY `buyer_uid` (`buyer_uid`),
  KEY `create_time` (`create_time`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='用户抽奖次数奖励日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_user_lottery_log`
--

LOCK TABLES `ly_user_lottery_log` WRITE;
/*!40000 ALTER TABLE `ly_user_lottery_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `ly_user_lottery_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_yuebao_list`
--

DROP TABLE IF EXISTS `ly_yuebao_list`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_yuebao_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL DEFAULT '',
  `lilv` varchar(266) NOT NULL DEFAULT '',
  `time` varchar(255) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_yuebao_list`
--

LOCK TABLES `ly_yuebao_list` WRITE;
/*!40000 ALTER TABLE `ly_yuebao_list` DISABLE KEYS */;
/*!40000 ALTER TABLE `ly_yuebao_list` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ly_yuebao_pay`
--

DROP TABLE IF EXISTS `ly_yuebao_pay`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ly_yuebao_pay` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) DEFAULT NULL,
  `yuebaoid` int(11) DEFAULT NULL COMMENT '余额宝产品ID',
  `lilv` varchar(50) DEFAULT NULL COMMENT '利率',
  `money` decimal(16,2) DEFAULT NULL COMMENT '金额',
  `daynum` int(11) DEFAULT NULL COMMENT '天数',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `status` char(1) DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `yuebaoid` (`yuebaoid`),
  KEY `uid` (`uid`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ly_yuebao_pay`
--

LOCK TABLES `ly_yuebao_pay` WRITE;
/*!40000 ALTER TABLE `ly_yuebao_pay` DISABLE KEYS */;
INSERT INTO `ly_yuebao_pay` VALUES (1,48,11,'0.8',10,5,'2022-05-27 14:02:44','2022-06-01 14:02:44','1'),(2,54,1,'0.01',50,1,'2022-05-27 14:09:31','2022-05-28 14:09:31','1'),(3,61,10,'0.05',100,1,'2024-10-14 17:14:41','2024-10-15 17:14:41','1'),(4,61,1,'0.01',100,1,'2024-10-14 17:14:51','2024-10-15 17:14:51','1'),(5,61,10,'0.05',5,1,'2024-10-14 17:15:00','2024-10-15 17:15:00','1'),(6,62,10,'0.05',200,1,'2024-10-14 17:18:41','2024-10-15 17:18:41','1');
/*!40000 ALTER TABLE `ly_yuebao_pay` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping events for database 'www_xyw64_top'
--

--
-- Dumping routines for database 'www_xyw64_top'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-12-14 19:08:11

-- ================================
-- 任务分佣系统改造完成
-- ================================
-- 1. ly_task 表已添加三级分佣字段 (task_rebate1, task_rebate2, task_rebate3)
-- 2. ly_task_tpl 表已添加三级分佣字段 (task_rebate1, task_rebate2, task_rebate3)
-- 3. ly_user_grade 表的分佣字段已标记为废弃
-- 4. 已添加索引优化查询性能 (idx_task_rebate)
-- 5. 默认分佣比例: 一级10%, 二级5%, 三级2%
-- ================================

-- ================================
-- 抽奖转盘系统配置完成
-- ================================
-- 1. ly_wheel 表已配置完整的9个奖品，包含完整多语言支持
-- 2. ly_user_grade 表已配置VIP等级抽奖次数 (daily_turntable_times)
--    - VIP等级1（普通会员）: 每日3次抽奖机会
--    - VIP等级2（白银会员）: 每日5次抽奖机会
-- 3. 奖品配置包含：
--    - 任务次数奖励 (type=1): 1次、2次、3次、5次、10次任务
--    - 现金奖励 (type=2): $1、$2现金
--    - 谢谢参与 (type=0): 无奖励
-- 4. 相关表结构完整：
--    - ly_wheel: 奖品配置表
--    - ly_wheel_config: 抽奖配置表
--    - ly_wheel_record: 抽奖记录表
--    - ly_user_lottery_times: 用户每日抽奖次数记录表
--    - ly_user_lottery_log: 用户抽奖次数奖励日志表
-- ================================

-- ========================================
-- 数据库更新脚本 - 2025-07-30
-- 资金流水表统计优化和VIP0限制机制
-- ========================================

-- 资金流水表统计优化索引
ALTER TABLE ly_trade_details ADD INDEX idx_uid_type_time (uid, trade_type, trade_time);
ALTER TABLE ly_trade_details ADD INDEX idx_time_type (trade_time, trade_type);
ALTER TABLE ly_trade_details ADD INDEX idx_source_uid_type (source_uid, trade_type);
ALTER TABLE ly_trade_details ADD INDEX idx_state_type (state, trade_type);
ALTER TABLE ly_trade_details ADD INDEX idx_uid_state (uid, state);

-- VIP0限制机制：修改VIP0的有效期为3天
UPDATE ly_user_grade SET validity_time = 0.1 WHERE grade = 1;