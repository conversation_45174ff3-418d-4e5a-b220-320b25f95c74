webpackJsonp([29],{UbIS:function(t,i,e){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n={render:function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("div",{staticClass:"Site PageBox"},[e("van-nav-bar",{attrs:{fixed:"",border:!1,title:t.showInfo.title,"left-arrow":""},on:{"click-left":function(i){return t.$router.go(-1)}}}),t._v(" "),e("div",{staticClass:"ScrollBox"},[e("div",{staticClass:"Content",staticStyle:{padding:"10px 20px 20px"},domProps:{innerHTML:t._s(t.showInfo.content)}})])],1)},staticRenderFns:[]};var o=e("VU/8")({name:"Info",components:{},props:["articleType","articleId"],data:function(){return{showInfo:{title:""}}},computed:{},watch:{},created:function(){var t=this;"video"==this.articleType&&(this.showInfo=this.InitData.videovTutorial.find(function(i){return i.id==t.articleId})),"help"==this.articleType&&(this.showInfo=this.InitData.helpList.find(function(i){return i.id==t.articleId})),"notice"==this.articleType&&(this.showInfo=this.InitData.noticelist.find(function(i){return i.id==t.articleId})),"terms"==this.articleType&&(this.showInfo=this.InitData.disclaimerList.find(function(i){return i.id==t.articleId}))},mounted:function(){},activated:function(){},destroyed:function(){},methods:{}},n,!1,function(t){e("Yzey")},"data-v-3ab7464b",null);i.default=o.exports},Yzey:function(t,i){}});