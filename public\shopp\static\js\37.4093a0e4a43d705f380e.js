webpackJsonp([37],{ZVhq:function(t,s,a){"use strict";Object.defineProperty(s,"__esModule",{value:!0});var e={render:function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("div",{staticClass:"PageBox"},[a("div",{staticClass:"ScrollBox"},[a("van-tabs",{attrs:{ellipsis:!1,border:!1,color:"#4087f1","title-active-color":"#4087f1",background:"#151d31","title-inactive-color":"#bbb","line-width":"60"},on:{change:t.changeTabs},model:{value:t.tabsIndex,callback:function(s){t.tabsIndex=s},expression:"tabsIndex"}},t._l(t.taskTabs,function(s){return a("van-tab",{key:s.state,attrs:{title:s.text}},[a("van-pull-refresh",{on:{refresh:t.onRefresh},model:{value:t.isRefresh,callback:function(s){t.isRefresh=s},expression:"isRefresh"}},[a("van-list",{class:{Empty:!t.listData[t.tabsIndex].length},attrs:{finished:t.isFinished,"finished-text":t.listData[t.tabsIndex].length?t.$t("vanPull[0]"):t.$t("vanPull[1]")},on:{load:t.onLoad},model:{value:t.isLoad,callback:function(s){t.isLoad=s},expression:"isLoad"}},t._l(t.listData[t.tabsIndex],function(s,e){return a("van-cell",{key:s.order_id,staticClass:"TaskItem",attrs:{"title-class":"record","value-class":"audit",border:!1,to:"/user/auditInfo/"+s.order_id},scopedSlots:t._u([{key:"title",fn:function(){return[a("h4",[t._v(t._s(s.title))]),t._v(" "),a("p",[t._v(t._s(t.$t("audit.default[1]"))+":"+t._s(s.o_username))]),t._v(" "),a("p",[t._v(t._s(t.$t("audit.default[2]"))+":"+t._s(s.add_time))]),t._v(" "),a("p",[t._v(t._s(t.$t("audit.default[3]"))+":"+t._s(0==t.tabsIndex?s.add_time:s.trial_time))])]},proxy:!0}],null,!0)},[a("div",{staticClass:"icon",attrs:{slot:"icon"},slot:"icon"},[a("h4",[t._v(t._s(s.group_name))]),t._v(" "),a("a",{attrs:{href:"javascript:;"},on:{click:function(a){return a.stopPropagation(),t.$Util.OpenUrl(s.link_info)}}},[a("img",{attrs:{src:t.InitData.setting.up_url+s.icon}})]),t._v(" "),a("van-tag",{attrs:{type:"primary"}},[t._v(t._s(s.vip_dec))])],1),t._v(" "),t._v(" "),a("p",{class:"state"+s.status},[t._v(t._s(s.status_dec))]),t._v(" "),a("p",[t._v(t._s(t.$Currency.getSymbol())),a("b",[t._v(t._s(Number(s.reward_price)))])]),t._v(" "),1==t.tabsIndex?a("van-button",{attrs:{type:"info",round:"",size:"mini"}},[t._v(t._s(t.$t("audit.default[4]")))]):t._e()],1)}),1)],1)],1)}),1)],1)])},staticRenderFns:[]};var i=a("VU/8")({name:"TaskRecord",components:{},props:["taskState"],data:function(){return{listData:"",isLoad:!1,isFinished:!1,isRefresh:!1,pageNo:1,tabsState:1,tabsIndex:0,taskTabs:[{state:1,text:this.$t("audit.tabs[0]")},{state:2,text:this.$t("audit.tabs[1]")},{state:3,text:this.$t("audit.tabs[2]")},{state:4,text:this.$t("audit.tabs[3]")}],fileList:[]}},computed:{},watch:{$route:function(){var t=this;this.taskState?(this.tabsIndex=this.taskTabs.findIndex(function(s){return s.state==t.taskState}),this.tabsState=this.taskState):(this.tabsState=1,this.tabsIndex=0),this.getListData("init")}},created:function(){var t=this;this.$parent.navBarTitle=this.$t("audit.default[0]"),this.listData=this.taskTabs.flatMap(function(t){return[""]}),this.taskState&&(this.tabsIndex=this.taskTabs.findIndex(function(s){return s.state==t.taskState}),this.tabsState=this.taskState),this.getListData("init")},mounted:function(){},activated:function(){},destroyed:function(){},methods:{onLoad:function(){this.getListData("load")},changeTabs:function(t){this.tabsState=this.taskTabs[t].state,this.$route.query.taskId?this.$router.push("/user/auditRecord/"+this.tabsState+"?taskId="+this.$route.query.taskId):this.$router.push("/user/auditRecord/"+this.tabsState)},getListData:function(t){var s=this;this.isLoad=!0,this.isRefresh=!1,"load"==t?this.pageNo+=1:(this.pageNo=1,this.isFinished=!1);var a={status:this.tabsState,page_no:this.pageNo,is_u:1};this.$route.query.taskId&&(a.task_id=this.$route.query.taskId),this.$Model.GetTaskRecord(a,function(a){s.isLoad=!1,1==a.code?(s.listData[s.tabsIndex]="load"==t?s.listData[s.tabsIndex].concat(a.info):a.info,s.pageNo==a.data_total_page?s.isFinished=!0:s.isFinished=!1):(s.listData[s.tabsIndex]="",s.isFinished=!0)})},onRefresh:function(){this.getListData("init")}}},e,!1,function(t){a("rpjw")},"data-v-23e9038d",null);s.default=i.exports},rpjw:function(t,s){}});