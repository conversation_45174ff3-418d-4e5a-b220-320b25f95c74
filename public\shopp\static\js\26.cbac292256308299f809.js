webpackJsonp([26],{"lGf+":function(t,a){},t6Dv:function(t,a,n){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var i={render:function(){var t=this,a=t.$createElement,n=t._self._c||a;return n("div",{staticClass:"PageBox"},[n("div",{staticClass:"ScrollBox"},[1!=t.userInfo.is_realname?n("div",{staticStyle:{overflow:"hidden","margin-top":"15px"}},[n("van-divider",{attrs:{hairline:!1}},[t._v(t._s(t.$t("bankCard.tips[0]")))]),t._v(" "),n("div",{staticStyle:{padding:"15px"}},[n("van-button",{staticClass:"loginBtn",staticStyle:{"font-size":"16px"},attrs:{block:"",type:"danger"},on:{click:function(a){return t.$router.push("/user/set/realname")}}},[t._v(t._s(t.$t("bankCard.default[1]")))])],1)],1):n("div",[t.showAdd?n("div",[n("van-form",{staticClass:"formBox"},[n("van-field",{attrs:{readonly:"",value:t.userInfo.realname,label:t.$t("bankCard.label[0]")}}),t._v(" "),n("van-field",{attrs:{readonly:"",label:t.$t("bankCard.label[1]"),placeholder:t.$t("bankCard.placeholder[0]")},on:{click:function(a){t.showPicker=!0}},model:{value:t.postData.bank_name,callback:function(a){t.$set(t.postData,"bank_name","string"==typeof a?a.trim():a)},expression:"postData.bank_name"}}),t._v(" "),n("van-field",{attrs:{label:t.isUSDTBank?t.$t("bankCard.label[7]"):t.$t("bankCard.label[2]"),placeholder:t.isUSDTBank?t.$t("bankCard.placeholder[6]"):t.$t("bankCard.placeholder[1]")},model:{value:t.postData.card_no,callback:function(a){t.$set(t.postData,"card_no","string"==typeof a?a.trim():a)},expression:"postData.card_no"}})],1),t._v(" "),n("div",{staticStyle:{padding:"15px"}},[n("van-button",{staticClass:"loginBtn",staticStyle:{"font-size":"16px"},attrs:{block:"",type:"danger",loading:t.isSubmit,"loading-text":t.$t("bankCard.default[2]")},on:{click:t.onSubmit}},[t._v(t._s(t.$t("bankCard.default[3]")))])],1)],1):n("div",[n("div",{staticStyle:{padding:"10px 10px 0"}},[n("van-button",{staticClass:"loginBtn",staticStyle:{"font-size":"16px"},attrs:{block:"",type:"info"},on:{click:function(a){t.showAdd=!0}}},[t._v(t._s(t.$t("usdt[2]")))])],1),t._v(" "),n("div",{staticClass:"listBox"},t._l(t.cardList,function(t){return n("van-cell",{key:t.id,staticClass:"list",attrs:{border:!1,title:t.bank_name+" "+t.bank_branch_name,label:t.card_no.replace(/^(.{4}).*(.{4})$/,"$1 **** **** $2"),icon:"card"}})}),1)])])]),t._v(" "),n("van-popup",{staticClass:"PickerPopup",attrs:{position:"bottom"},model:{value:t.showPicker,callback:function(a){t.showPicker=a},expression:"showPicker"}},[n("van-picker",{attrs:{"show-toolbar":"",columns:t.bankList},on:{confirm:t.onConfirm,cancel:function(a){t.showPicker=!1}}})],1)],1)},staticRenderFns:[]};var e=n("VU/8")({name:"BankCard",components:{},props:[],data:function(){return{postData:{name:"",bank_name:"",card_no:"",bank_id:""},showPicker:!1,bankList:"",showAdd:!1,isSubmit:!1}},computed:{userInfo:function(){return this.$store.state.UserInfo},cardList:function(){return this.$store.state.BankCardList},InitData:function(){return this.$store.state.InitData},isUSDTBank:function(){return this.postData.bank_name&&(this.postData.bank_name.toUpperCase().includes("USDT")||this.postData.bank_name.toUpperCase().includes("TETHER"))}},watch:{"InitData.BanksList":{handler:function(t){t&&t.length>0&&(this.bankList=t.filter(function(t){return t.enabled}).map(function(t){return t.bank}))},immediate:!0}},created:function(){this.$parent.navBarTitle=this.$t("usdt[1]"),this.cardList.length?this.showAdd=!1:this.showAdd=!0,this.postData.name=this.userInfo.realname,this.InitData.BanksList&&this.InitData.BanksList.length>0&&(this.bankList=this.InitData.BanksList.filter(function(t){return t.enabled}).map(function(t){return t.bank})),this.$Model.GetBankCardList()},mounted:function(){},activated:function(){},destroyed:function(){},methods:{onConfirm:function(t,a){this.postData.bank_name=t;var n=this.InitData.BanksList.filter(function(t){return t.enabled});n[a]&&(this.postData.bank_id=n[a].bank_id),this.showPicker=!1},onSubmit:function(){var t=this;if(this.postData.bank_name)if(this.postData.card_no)this.isSubmit=!0,this.$Model.AddBankCard(this.postData,function(a){t.isSubmit=!1,1==a.code&&(t.showAdd=!1)});else{var a=this.isUSDTBank?this.$t("bankCard.placeholder[6]"):this.$t("bankCard.placeholder[1]");this.$Dialog.Toast(a)}else this.$Dialog.Toast(this.$t("bankCard.placeholder[0]"))}}},i,!1,function(t){n("lGf+")},"data-v-5d4e1d40",null);a.default=e.exports}});