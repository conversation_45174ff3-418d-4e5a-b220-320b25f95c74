<?php

namespace app\api\controller;

use think\Cache;

use app\api\controller\BaseController;



class CommonController extends BaseController{

    //获取平台共用数据
    public function BackData(){
        $param 	=	input('param.');
        $lang	=	isset($param['lang']) && $param['lang'] ? $param['lang'] : 'id';
        //网站公告
        $noticelist		= model('Notice')->where(array(['state','=',1],['lang','=',$lang]))->order('add_time','desc')->select()->toArray();
        $data			=	[];
        $k=$l=$j=$s=0;
        $data['info']['noticelist'] =	[];
        $data['info']['helpList'] =	[];
        $data['info']['videovTutorial'] =	[];
        $data['info']['serviceList'] =	[];
        foreach ($noticelist as $key => $value) {

            switch($value['gropid']){
                case 1:
                    $data['info']['noticelist'][$k]['id']   			= $value['id'];
                    $data['info']['noticelist'][$k]['title']   			= $value['title'];
                    $data['info']['noticelist'][$k]['content']   		= htmlspecialchars_decode($value['content']);
                    $data['info']['noticelist'][$k]['add_time'] 		= date('Y-m-d H:i:s',$value['add_time']);
                    ++$k;
                    break;
                case 2:
                    $data['info']['helpList'][$l]['id']   			= $value['id'];
                    $data['info']['helpList'][$l]['title']   			= $value['title'];
                    $data['info']['helpList'][$l]['content']   		= htmlspecialchars_decode($value['content']);
                    $data['info']['helpList'][$l]['add_time'] 		= date('Y-m-d H:i:s',$value['add_time']);
                    ++$l;
                    break;
                case 3:
                    $data['info']['videovTutorial'][$j]['id']   			= $value['id'];
                    $data['info']['videovTutorial'][$j]['title']   			= $value['title'];
                    $data['info']['videovTutorial'][$j]['content']   		= htmlspecialchars_decode($value['content']);
                    $data['info']['videovTutorial'][$j]['add_time'] 		= date('Y-m-d H:i:s',$value['add_time']);
                    ++$j;
                    break;
                case 4:
                    $data['info']['serviceList'][$s]['id']   			= $value['id'];
                    $data['info']['serviceList'][$s]['title']   			= $value['title'];
                    $data['info']['serviceList'][$s]['content']   		= htmlspecialchars_decode($value['content']);
                    $data['info']['serviceList'][$s]['add_time'] 		= date('Y-m-d H:i:s',$value['add_time']);
                    $data['info']['serviceList'][$s]['url']   			= $value['url'];
                    $data['info']['serviceList'][$s]['cover_img']   			= $value['cover_img'];
                    ++$s;
                    break;
            }
        }


        $headData	= ['head_1.png', 'head_2.png', 'head_3.png', 'head_4.png', 'head_5.png', 'head_6.png', 'head_7.png', 'head_8.png', 'head_9.png', 'head_10.png'];

        $usernameData	= ['130','131','132','133','134','135','136','137','138','139','145','146','147','150','151','152','153','155','156','157','158','159','162','165','166'];

        // 最新播报
        //	$UserVipData	= model('UserVip')->where(array(['etime','>=',strtotime(date("Y-m-d",time()))],['state','=',1]))->order('etime','DESC')->limit(10)->select()->toArray();
        $UserVipData	= model('UserIndex')->where(array(['trade_type','=',8]))->order('id','DESC')->limit(10)->select()->toArray();
        $userviplist	=[];
        if($UserVipData){
            foreach ($UserVipData as $key2 => $value2) {

                $userviplist[$key2]['username']			= substr(trim($value2['username']),0,0).'****'.substr(trim($value2['username']),-4);

                $child_vip_name = '';

                $child = model('users')->where('id', '=', $value2['sid'])->find();
                if ($child) {
                    $child_vip = model('user_grade')->where('grade', '=', $child['vip_level'])->find();

                    if ($child_vip) {

                        if($lang=='en'){
                            $child_vip_name = $child_vip['en_name'];
                        }elseif($lang=='cn') {
                            $child_vip_name = $child_vip['name'];
                        }elseif($lang=='ft') {
                            $child_vip_name = $child_vip['ft_name'];
                        }elseif($lang=='id') {
                            $child_vip_name = $child_vip['ydn_name'];
                        }elseif($lang=='vi') {
                            $child_vip_name = $child_vip['yn_name'];
                        }elseif($lang=='es') {
                            $child_vip_name = $child_vip['xby_name'];
                        }elseif($lang=='jp') {
                            $child_vip_name = $child_vip['ry_name'];
                        }elseif($lang=='th') {
                            $child_vip_name = $child_vip['ty_name'];
                        }elseif($lang=='yd') {
                            $child_vip_name = $child_vip['yd_name'];
                        }elseif($lang=='ma') {
                            $child_vip_name = $child_vip['ma_name'];
                        }elseif($lang=='pt') {
                            $child_vip_name = $child_vip['pt_name'];
                        }

                    }

                }

                $userviplist[$key2]['child_vip_name'] = $child_vip_name;

                if($lang=='en'){
                    $userviplist[$key2]['name']			= $value2['trade_amount'];
                }else{
                    $userviplist[$key2]['name']			= $value2['trade_amount'];
                }
            }
        }
        $userviplist2	=	[];

        $data['info']['userviplist']	=	array_merge($userviplist,$userviplist2);
        // 会员榜单
        for ($i=0;$i < 20;$i++) {

            $headKey		= array_rand($headData);
            $headerImage	= $headData[$headKey];

            $nameKey		= array_rand($usernameData);
            $username		= $usernameData[$nameKey];

            $data['info']['memberList'][$i]['username']		= '****'.mt_rand(1000,9999);
            $data['info']['memberList'][$i]['header'] 		= $headerImage;
            $data['info']['memberList'][$i]['number'] 		= mt_rand(10,100);
            $data['info']['memberList'][$i]['profit'] 		= round($data['info']['memberList'][$i]['number'] * 1.8,3);

        }

        // 商家榜单
        for ($j=0;$j < 20;$j++) {

            $headKey		= array_rand($headData);
            $headerImage	= $headData[$headKey];

            $nameKey		= array_rand($usernameData);
            $username		= $usernameData[$nameKey];

            $data['info']['businessList'][$j]['username']		= '****'.mt_rand(1000,9999);
            $data['info']['businessList'][$j]['header'] 		= $headerImage;
            $data['info']['businessList'][$j]['number'] 		= mt_rand(1000,9999);
            $data['info']['businessList'][$j]['profit'] 		= round($data['info']['businessList'][$j]['number'] * 2,3);

        }
        //任务类型
        $taskclasslist = model('TaskClass')->where(array(['state','=',1]))->order('num','ASC')->select()->toArray();
        $TaskClassdata = [];
        foreach ($taskclasslist as $key => $value) {
            $TaskClassdata[$key]['group_id']   			= $value['id'];
            $TaskClassdata[$key]['icon']   				= $value['h_icon'];

            if($lang=='en'){
                $TaskClassdata[$key]['group_name']			= $value['group_name_en'];
                $TaskClassdata[$key]['group_info']			= $value['group_info_en'];
                $TaskClassdata[$key]['h_group_name']		= $value['group_name_en'];
                $TaskClassdata[$key]['h_group_info']		= $value['group_info_en'];
            }elseif($lang=='cn'){
                $TaskClassdata[$key]['group_name']			= $value['group_name'];
                $TaskClassdata[$key]['group_info']			= $value['group_info'];
                $TaskClassdata[$key]['h_group_name']		= $value['group_name'];
                $TaskClassdata[$key]['h_group_info']		= $value['group_info'];
            }elseif($lang=='id'){
                $TaskClassdata[$key]['group_name']			= $value['group_name_ydn'];
                $TaskClassdata[$key]['group_info']			= $value['group_info_ydn'];
                $TaskClassdata[$key]['h_group_name']		= $value['group_name_ydn'];
                $TaskClassdata[$key]['h_group_info']		= $value['group_info_ydn'];
            }elseif($lang=='ft'){
                $TaskClassdata[$key]['group_name']			= $value['group_name_ft'];
                $TaskClassdata[$key]['group_info']			= $value['group_info_ft'];
                $TaskClassdata[$key]['h_group_name']		= $value['group_name_ft'];
                $TaskClassdata[$key]['h_group_info']		= $value['group_info_ft'];
            }elseif($lang=='vi'){
                $TaskClassdata[$key]['group_name']			= $value['group_name_yn'];
                $TaskClassdata[$key]['group_info']			= $value['group_info_yn'];
                $TaskClassdata[$key]['h_group_name']		= $value['group_name_yn'];
                $TaskClassdata[$key]['h_group_info']		= $value['group_info_yn'];
            }elseif($lang=='ja'){
                $TaskClassdata[$key]['group_name']			= $value['group_name_ry'];
                $TaskClassdata[$key]['group_info']			= $value['group_info_ry'];
                $TaskClassdata[$key]['h_group_name']		= $value['group_name_ry'];
                $TaskClassdata[$key]['h_group_info']		= $value['group_info_ry'];
            }elseif($lang=='es'){
                $TaskClassdata[$key]['group_name']			= $value['group_name_xby'];
                $TaskClassdata[$key]['group_info']			= $value['group_info_xby'];
                $TaskClassdata[$key]['h_group_name']		= $value['group_name_xby'];
                $TaskClassdata[$key]['h_group_info']		= $value['group_info_xby'];
            }elseif($lang=='th'){
                $TaskClassdata[$key]['group_name']			= $value['group_name_ty'];
                $TaskClassdata[$key]['group_info']			= $value['group_info_ty'];
                $TaskClassdata[$key]['h_group_name']		= $value['group_name_ty'];
                $TaskClassdata[$key]['h_group_info']		= $value['group_info_ty'];
            }elseif($lang=='yd'){
                $TaskClassdata[$key]['group_name']			= $value['group_name_yd'];
                $TaskClassdata[$key]['group_info']			= $value['group_info_yd'];
                $TaskClassdata[$key]['h_group_name']		= $value['group_name_yd'];
                $TaskClassdata[$key]['h_group_info']		= $value['group_info_yd'];
            }elseif($lang=='ma'){
                $TaskClassdata[$key]['group_name']			= $value['group_name_ma'];
                $TaskClassdata[$key]['group_info']			= $value['group_info_ma'];
                $TaskClassdata[$key]['h_group_name']		= $value['group_name_ma'];
                $TaskClassdata[$key]['h_group_info']		= $value['group_info_ma'];
            }elseif($lang=='pt'){
                $TaskClassdata[$key]['group_name']			= $value['group_name_pt'];
                $TaskClassdata[$key]['group_info']			= $value['group_info_pt'];
                $TaskClassdata[$key]['h_group_name']		= $value['group_name_pt'];
                $TaskClassdata[$key]['h_group_info']		= $value['group_info_pt'];
            }





            $TaskClassdata[$key]['state']				= $value['state'];
            $TaskClassdata[$key]['h_icon']   			= $value['h_icon'];
            $TaskClassdata[$key]['is_f']				= $value['is_f'];
            $TaskClassdata[$key]['is_fx']				= $value['is_fx'];
        }
        $data['info']['taskclasslist'] = $TaskClassdata;


        $data['info']['setting'] = model('Setting')->find();
        $data['info']['setting']['up_url'] = $data['info']['setting']['q_server_name'];
        $data['info']['currency']=$data['info']['setting']['currency'];
        //会员等级 - 只显示未隐藏的VIP等级
        $UserViplist = model('UserGrade')->where(array('state'=>1, 'is_hidden'=>0))->order('id','ASC')->select()->toArray();
        $UserViplistdata = [];
        foreach ($UserViplist as $key => $value) {
            $UserViplistdata[$key]['grade']				= $value['grade'];
            $UserViplistdata[$key]['amount']			= $value['amount'];
            if($lang=='en'){
                $UserViplistdata[$key]['name']			= $value['en_name'];
                $UserViplistdata[$key]['privilege_description'] = $value['privilege_description_en'] ?: '';
            }elseif($lang=='cn'){
                $UserViplistdata[$key]['name']			= $value['name'];
                $UserViplistdata[$key]['privilege_description'] = $value['privilege_description'] ?: '';
            }elseif($lang=='ft'){
                $UserViplistdata[$key]['name']			= $value['ft_name'];
                $UserViplistdata[$key]['privilege_description'] = $value['privilege_description_ft'] ?: '';
            }elseif($lang=='ja'){
                $UserViplistdata[$key]['name']			= $value['ry_name'];
                $UserViplistdata[$key]['privilege_description'] = $value['privilege_description_ry'] ?: '';
            }elseif($lang=='id'){
                $UserViplistdata[$key]['name']			= $value['ydn_name'];
                $UserViplistdata[$key]['privilege_description'] = $value['privilege_description_ydn'] ?: '';
            }elseif($lang=='vi'){
                $UserViplistdata[$key]['name']			= $value['yn_name'];
                $UserViplistdata[$key]['privilege_description'] = $value['privilege_description_yn'] ?: '';
            }elseif($lang=='es'){
                $UserViplistdata[$key]['name']			= $value['xby_name'];
                $UserViplistdata[$key]['privilege_description'] = $value['privilege_description_xby'] ?: '';
            }elseif($lang=='th'){
                $UserViplistdata[$key]['name']			= $value['ty_name'];
                $UserViplistdata[$key]['privilege_description'] = $value['privilege_description_ty'] ?: '';
            }elseif($lang=='yd'){
                $UserViplistdata[$key]['name']			= $value['yd_name'];
                $UserViplistdata[$key]['privilege_description'] = $value['privilege_description_yd'] ?: '';
            }elseif($lang=='ma'){
                $UserViplistdata[$key]['name']			= $value['ma_name'];
                $UserViplistdata[$key]['privilege_description'] = $value['privilege_description_ma'] ?: '';
            }elseif($lang=='pt'){
                $UserViplistdata[$key]['name']			= $value['pt_name'];
                $UserViplistdata[$key]['privilege_description'] = $value['privilege_description_pt'] ?: '';
            }
            $UserViplistdata[$key]['number']			= $value['number'];
            // 添加抽奖次数字段
            $UserViplistdata[$key]['daily_turntable_times'] = $value['daily_turntable_times'] ?: 0;
            // 添加VIP状态字段
            $UserViplistdata[$key]['is_hidden']			= $value['is_hidden'];
            $UserViplistdata[$key]['is_locked']			= $value['is_locked'];
            $UserViplistdata[$key]['can_purchase']		= ($value['is_locked'] == 0) ? true : false;
            // commission字段已移除，改为任务级别设置
            // $UserViplistdata[$key]['commission']		= $value['commission'];
            // $UserViplistdata[$key]['income']			= $value['number'] * $value['commission'];
            // $UserViplistdata[$key]['income1']			= $value['number'] * $value['commission'] * 30 ;
        }
        $data['info']['UserGradeList'] = $UserViplistdata;

        $authenticationdata	=	[];
        switch($lang){
            case 'en':
                $authenticationdata = ['Mobile phone authentication','Wechat authentication','Real name authentication','Identity authentication'];
                $data['info']['drawNotice'][0] = [
                    'title' => 'Tips',
                    'content'=> "1、Minimum withdrawal{$data['info']['currency']}{$data['info']['setting']['min_w']}.<br/>2、Cash withdrawal shall be received within 24 hours.<br/>3、Cash can only be withdrawn once a day."
                ];
                break;
            case 'cn':
                $authenticationdata	=['手机认证','微信认证','实名认证','身份认证'];
                $data['info']['drawNotice'][0] = [
                    'title' => '温馨提示',
                    'content'=> "1、最低提现{$data['info']['currency']}{$data['info']['setting']['min_w']}。<br/>2、提现24小时内到账。<br/>3、一天只能提现一次。"
                ];
                break;
            case 'ft':
                $authenticationdata	=['手機認證','微信認證','實名認證','身份認證'];
                $data['info']['drawNotice'][0] = [
                    'title' => '溫馨提示',
                    'content'=> "1、最低提現{$data['info']['currency']}{$data['info']['setting']['min_w']}。<br/>2、提現24小時內到賬。<br/>3、一天只能提現一次。"
                ];
                break;
            case 'vi':
                $authenticationdata	=['Xác thc din thoi','Xác thc chat','Xác thc tên tht','Xác thc danh tính'];
                $data['info']['drawNotice'][0] = [
                    'title' => 'nhắc',
                    'content'=> "1、Thu hồi tối thiểu{$data['info']['currency']}{$data['info']['setting']['min_w']}.<br/>2、Việc rút tiền sẽ được nhận trong vòng một ngày.<br/>3、Tiền mặt chỉ được rút ra một lần mỗi ngày."
                ];
                break;
            case 'id':
                $authenticationdata	=['Otentikasi ponsel','Otentikasi Wechat','Autentikasi nama asli','autentikasi identitas'];
                $data['info']['drawNotice'][0] = [
                    'title' => 'peringatan',
                    'content'=> "1、Pengunduran minimum{$data['info']['currency']}{$data['info']['setting']['min_w']}。<br/>2、Penerimaan uang tunai akan diterima dalam 24 jam.<br/>3、Uang tunai hanya bisa ditarik sekali sehari."
                ];
                break;
            case 'es':
                $authenticationdata	=['Autenticación de teléfonos','Autenticación de micro carta','Homologación real','Identificación:'];
                $data['info']['drawNotice'][0] = [
                    'title' => 'Consejos cálidos',
                    'content'=> "1、Presentación mínima{$data['info']['currency']}{$data['info']['setting']['min_w']}.<br/>2、La retirada de efectivo debe recibirse en un plazo de 24 horas.<br/>3、El efectivo sólo se puede retirar una vez al día."
                ];
                break;
            case 'ja':
                $authenticationdata	=['携帯電話の認証','WeChat認証','実名認証','認証'];
                $data['info']['drawNotice'][0] = [
                    'title' => '暖かいヒント',
                    'content'=> "1、最低現金化{$data['info']['currency']}{$data['info']['setting']['min_w']}。<br/>2、現金で24時間以内に入金します。<br/>3、1日に1回しか現金化できません。"
                ];
                break;
            case 'yd':
                $authenticationdata	=['  ', 'wechat ', '  ', ' '];
                $data['info']['drawNotice'][0] = [
                    'title' => 'सचेतक',
                    'content'=> "1、न्यूनतम घटना{$data['info']['currency']}{$data['info']['setting']['min_w']}。<br/>2、24 घंटे में पैसा निकालना प्राप्त होगा.<br/>3、पैसा केवल एक दिन एक बार हटाया जा सकता है."
                ];
                break;
            case 'ma':
                $authenticationdata	=['Pengesahan mudah alih', 'Pengesahan WeChat', 'Pengesahan nama sebenar', 'Pengesahan identiti'];
                $data['info']['drawNotice'][0] = [
                    'title' => 'reminder',
                    'content'=> "1、Pengunduran minimum{$data['info']['currency']}{$data['info']['setting']['min_w']}.<br/>2、Pengunduran wang akan diterima dalam 24 jam.<br/>3、Duit hanya boleh ditarik sekali sehari."
                ];
                break;
            case 'pt':
                $authenticationdata	=['Autenticação por telefone celular','Autenticação Wechat','Autenticação do Nome verdadeiro','Autenticação Da identidade'];
                $data['info']['drawNotice'][0] = [
                    'title' => 'lembrete',
                    'content'=> "1、Retirada mínima{$data['info']['currency']}{$data['info']['setting']['min_w']}.<br/>2、A retirada de dinheiro será recebida dentro de 24 horas.<br/>3、O dinheiro só pode ser levantado uma vez por dia."
                ];
                break;
        }

        $data['info']['authenticationList']	=	$authenticationdata;

        //获取可提现银行列表 - 从配置文件读取bank_code_mapping
        try {
            // 使用正确的路径获取配置文件
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            $paymentConfig = include($configPath);
            $bankMapping = $paymentConfig['bank_code_mapping'] ?? [];

            $BanksList = [];
            $index = 0;

            foreach($bankMapping as $bankName => $codes){
                $BanksList[$index]['bank_id'] = $codes['bank_id'] ?? ($index + 1);
                $BanksList[$index]['bank'] = $bankName;
                $BanksList[$index]['bank_name_id'] = $bankName;
                $BanksList[$index]['jayapay_code'] = $codes['jayapay'] ?? '';
                $BanksList[$index]['watchpay_code'] = $codes['watchpay'] ?? '';
                $BanksList[$index]['types'] = 4;
                $BanksList[$index]['enabled'] = true;
                $index++;
            }

            // 添加USDT提现类型
            $BanksList[$index]['bank_id'] = 999;
            $BanksList[$index]['bank'] = 'USDT';
            $BanksList[$index]['bank_name_id'] = 'USDT';
            $BanksList[$index]['jayapay_code'] = 'USDT';
            $BanksList[$index]['watchpay_code'] = 'USDT';
            $BanksList[$index]['types'] = 5; // 使用不同的类型标识USDT
            $BanksList[$index]['enabled'] = true;

        } catch (\Exception $e) {
            // 如果配置文件读取失败，使用原来的数据库方式作为备用
            $payBanks = model('Bank')->where(array(['q_state','=',1],['pay_type','=',4]))->group('bank_name')->select();
            $BanksList = [];

            foreach($payBanks as $key =>$value){
                $BanksList[$key]['bank_id'] = $value['id'];
                $BanksList[$key]['bank'] = $value['bank_name'];
                $BanksList[$key]['types'] =  $value['pay_type'];
            }
        }

        $data['info']['BanksList'] = $BanksList;

        /**
         * 获取幻灯片
         */
        $slideLikst = model('Slide')->where(array(['status','=',1],['lang','=',$lang]))->select()->toArray();
        $data['info']['bannerList'] = [];
        foreach ($slideLikst as $key => $value) {
            $data['info']['bannerList'][$key] = $value['img_path'];
        }

        $data['info']['link']	=	['http://'.$_SERVER['HTTP_HOST'],'http://'.$_SERVER['HTTP_HOST'],'http://'.$_SERVER['HTTP_HOST']];

        // 获取信用界面显示控制设置
        $setting = model('Setting')->field('show_credit_interface')->find();
        $data['info']['show_credit_interface'] = $setting ? (int)$setting['show_credit_interface'] : 1;

        return json($data);
    }

    public function GetLanguage(){
        $data = model('Setting')->field('default_language')->find();
        switch ($data['default_language']){
            case 'cn':
                $data['default_language'] = 'zh-CN';
                break;
            case 'ft':
                $data['default_language'] = 'zh-TW';
                break;
            case 'en':
                $data['default_language'] = 'en-US';
                break;
            case 'vi':
                $data['default_language'] = 'vi-VN';
                break;
            case 'th':
                $data['default_language'] = 'th-TH';
                break;
            case 'id':
                $data['default_language'] = 'id-ID';
                break;
            case 'ja':
                $data['default_language'] = 'ja-JP';
                break;
            case 'es':
                $data['default_language'] = 'es-ES';
                break;
            case 'yd':
                $data['default_language'] = 'yd-YD';
                break;
            case 'yd':
                $data['default_language'] = 'yd-YD';
                break;
            case 'ma':
                $data['default_language'] = 'ma-MA';
                break;
            case 'pt':
                $data['default_language'] = 'pt-PT';
                break;
        }
        return json(['Language'=>$data]);
    }
}
