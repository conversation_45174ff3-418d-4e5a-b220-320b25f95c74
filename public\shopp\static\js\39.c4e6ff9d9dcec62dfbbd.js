webpackJsonp([39],{TKmG:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r={render:function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("div",{staticClass:"PageBox"},[e("div",{staticClass:"ScrollBox"},[e("div",{staticClass:"CustomCell"},[e("van-cell",{staticClass:"mt15",attrs:{icon:"cluster",title:""+t.$t("dayReport[1]")+t.$Currency.getSymbol()+t.reportData.myTotalProfit,value:t.$Util.DateFormat("YY-MM-DD",new Date)}}),t._v(" "),e("van-grid",{staticClass:"MyEarnings",attrs:{"column-num":2,border:!1,gutter:"1"}},[e("van-grid-item",{scopedSlots:t._u([{key:"icon",fn:function(){return[t._v("\n            "+t._s(t.$t("dayReport[2]"))+"("+t._s(t.$t("dayReport[6]"))+")\n          ")]},proxy:!0},{key:"text",fn:function(){return[t._v("\n            "+t._s(t.reportData.myTaskFinish)+"\n          ")]},proxy:!0}])}),t._v(" "),e("van-grid-item",{scopedSlots:t._u([{key:"icon",fn:function(){return[t._v("\n            "+t._s(t.$t("dayReport[3]"))+"("+t._s(t.$Currency.getSymbol())+")\n          ")]},proxy:!0},{key:"text",fn:function(){return[t._v("\n            "+t._s(t.reportData.myTaskProfit)+"\n          ")]},proxy:!0}])}),t._v(" "),e("van-grid-item",{scopedSlots:t._u([{key:"icon",fn:function(){return[t._v("\n            "+t._s(t.$t("dayReport[4]"))+"("+t._s(t.$t("dayReport[6]"))+")\n          ")]},proxy:!0},{key:"text",fn:function(){return[t._v("\n            "+t._s(t.reportData.branchTaskFinish)+"\n          ")]},proxy:!0}])}),t._v(" "),e("van-grid-item",{scopedSlots:t._u([{key:"icon",fn:function(){return[t._v("\n            "+t._s(t.$t("dayReport[5]"))+"("+t._s(t.$Currency.getSymbol())+")\n          ")]},proxy:!0},{key:"text",fn:function(){return[t._v("\n            "+t._s(t.reportData.branchTaskProfit)+"\n          ")]},proxy:!0}])})],1)],1),t._v(" "),e("div",{staticClass:"CustomCell"},[e("van-cell",{staticClass:"mt10",attrs:{border:!1,icon:"cluster",title:t.$t("dayReport[0]"),value:t.$t("dayReport[7]")}}),t._v(" "),e("table",{attrs:{width:"100%"}},[e("thead",[e("tr",[e("th",[t._v(t._s(t.$t("dayReport[8]")))]),t._v(" "),e("th",[t._v(t._s(t.$t("dayReport[9]")))]),t._v(" "),e("th",[t._v(t._s(t.$t("dayReport[10]")))]),t._v(" "),e("th",[t._v(t._s(t.$t("dayReport[11]")))]),t._v(" "),e("th",[t._v(t._s(t.$t("dayReport[12]")))])])]),t._v(" "),e("tbody",t._l(t.reportData.daily,function(n,r){return e("tr",{key:r},[e("td",[t._v(t._s(n.count))]),t._v(" "),e("td",[e("em",[t._v(t._s(n.task))])]),t._v(" "),e("td",[t._v(t._s(n.branch))]),t._v(" "),e("td",[e("em",[t._v(t._s(n.consume))])]),t._v(" "),e("td",[t._v(t._s(n.date))])])}),0)])],1)])])},staticRenderFns:[]};var a=e("VU/8")({name:"DayReport",components:{},props:[],data:function(){return{reportData:{myTotalProfit:"0.00",myTaskFinish:"0",myTaskProfit:"0.00",branchTaskFinish:"0",branchTaskProfit:"0.00",daily:[]}}},computed:{},watch:{},created:function(){var t=this;this.$parent.navBarTitle=this.$t("dayReport[0]"),this.$Model.DailyReport(function(n){1==n.code&&(t.reportData=n.data)})},mounted:function(){},activated:function(){},destroyed:function(){},methods:{}},r,!1,function(t){e("npJn")},"data-v-16232ddd",null);n.default=a.exports},npJn:function(t,n){}});