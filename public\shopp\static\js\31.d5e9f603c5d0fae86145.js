webpackJsonp([31],{AoAt:function(e,a,t){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var s={render:function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"PageBox"},[t("div",{staticClass:"ScrollBox"},["realname"==e.setType||"info"==e.setType?t("div",{staticClass:"fieldBox"},[t("van-field",{staticClass:"mt15",attrs:{size:"large",label:e.$t("userSet.label[0]"),placeholder:e.$t("userSet.placeholder[0]"),readonly:1==e.UserInfo.is_realname,clearable:""},model:{value:e.realName,callback:function(a){e.realName=a},expression:"realName"}})],1):e._e(),e._v(" "),"alipay"==e.setType?t("div",{staticClass:"fieldBox"},[t("van-field",{staticClass:"mt15",attrs:{size:"large",label:e.$t("userSet.label[2]"),placeholder:e.$t("userSet.placeholder[2]"),readonly:!!e.UserInfo.alipay,clearable:""},model:{value:e.alipayId,callback:function(a){e.alipayId=a},expression:"alipayId"}}),e._v(" "),t("van-field",{attrs:{size:"large",label:e.$t("userSet.label[3]"),placeholder:e.$t("userSet.placeholder[3]"),clearable:"",readonly:!!e.UserInfo.alipay_name},model:{value:e.alipayName,callback:function(a){e.alipayName=a},expression:"alipayName"}})],1):e._e(),e._v(" "),t("div",{staticStyle:{padding:"20px 20px"}},[t("van-button",{staticClass:"loginBtn",staticStyle:{"font-size":"16px"},attrs:{type:"danger",block:""},on:{click:e.setUserInfo}},[e._v(e._s(e.$t("userSet.default[3]")))])],1)])])},staticRenderFns:[]};var l=t("VU/8")({name:"Set",components:{},props:["setType"],data:function(){return{realName:"",qqNumber:"",alipayId:"",alipayName:"",postData:{}}},computed:{},watch:{},created:function(){"realname"==this.setType&&(this.$parent.navBarTitle=this.$t("userSet.default[0]"),this.realName=this.UserInfo.realname),"info"==this.setType&&(this.$parent.navBarTitle=this.$t("userSet.default[1]"),this.realName=this.UserInfo.realname),"alipay"==this.setType&&(this.$parent.navBarTitle=this.$t("userSet.default[2]"),this.alipayId=this.UserInfo.alipay,this.alipayName=this.UserInfo.alipay_name),this.$Model.GetUserInfo()},mounted:function(){},activated:function(){},destroyed:function(){},methods:{setUserInfo:function(){"realname"==this.setType&&(this.postData.realname=this.realName),"info"==this.setType&&(this.postData.realname=this.realName),"alipay"==this.setType&&(this.postData.alipay=this.alipayId,this.postData.alipay_name=this.alipayName),this.$Model.SetUserInfo(this.postData)}}},s,!1,function(e){t("jNze")},"data-v-38781362",null);a.default=l.exports},jNze:function(e,a){}});